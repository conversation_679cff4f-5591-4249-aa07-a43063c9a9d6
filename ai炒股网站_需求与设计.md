# AI炒股网站 — 需求文档（Detailed Requirements）

> 版本：1.0  
> 作者：产品经理（ChatGPT 代写）  
> 日期：2025-09-06

## 目录
1. 概览与目标
2. 范围与约束
3. 目标用户与使用场景
4. 用户故事
5. 功能性需求（详细）
6. 非功能性需求
7. 数据需求与来源
8. 接口与数据模型（概要）
9. 权限、合规与风控要求
10. 验收标准与KPI
11. 迭代计划与里程碑
12. 交付物清单

---

## 1 概览与目标
**产品名称（临时）**：AI热点板块分析（Web）

**产品愿景**：基于结构化行情数据、舆情与资金流向，通过可解释的“AI观点（LLM）+可视化”展示，帮助用户快速识别近期热门板块、板块龙头，并理解短期/中期的驱动因素与趋势。产品**只提供数据与分析参考**，不作为投资建议或交易委托。

**主要目标**：
- 自动识别并展示“最近热门板块”与“板块龙头”及其涨跌与趋势。
- 提供可解释、可追溯的 AI 分析结论（通过 RAG 模式，所有结论应基于结构化检索结果）。
- 提供交互式数据探索（排序、筛选、导出）与告警功能。


## 2 范围与约束
- 初始覆盖市场：可选 A 股 或 美股（上线前明确）。
- 不直接提供交易下单、也不提供法律/投资建议。
- 行情数据必须通过授权的数据供应商（生产环境）或合规免费源（PoC）获取。
- LLM 调用需对成本、速度和合规做控制，优先使用 RAG（检索增强生成）。


## 3 目标用户与使用场景
**用户画像**：散户（快速识热点）、职业投资者（数据深度）、研究人员和内容创作者。

**使用场景**：
- 早盘/盘中用户查看今日最热的板块与龙头股；
- 用户向 AI 提问“为什么半导体走强？”，得到有数据引用的解释；
- 高级用户配置自定义热度判定规则并回测。


## 4 用户故事（示例）
- 作为散户，我希望首页能自动显示当日5个最热板块及其龙头票，便于一眼判断热点。
- 作为专业用户，我希望能自定义热度因子并回测过去6个月的策略收益率。
- 作为用户，我希望在 AI 给出的原因中看到每条结论的数据支持与时间戳。


## 5 功能性需求（详细）
### 5.1 首页（Dashboard）
- 显示市场总览（主要指数涨跌、总成交额）。
- 热点板块卡片（默认展示10条，可分页）包含：板块名、热度分数（0-100）、当日涨跌%、成交额、本周期 sparkline（30日）以及 top3 龙头（名称+今日涨跌%）。
- AI观点卡（基于当前检索结果给出 1-3 点短结论，并列出每点的一条数据依据）。
- 动态刷新（默认 30s；可配置）。

### 5.2 板块详情页
- 板块指数 K 线（可切换 1d/7d/30d/90d/1y）。
- 成员股票表（列：序号/代码/名称/最新价/涨跌%/成交额/换手/量比/市值/是否龙头），支持排序、筛选、分页。
- 龙头股票展示（卡片形式，展示 7/30 日趋势、成交量、量比等）。
- 热度分解图（展示各个因子贡献）。
- AI 问答入口（向 LLM 提问板块驱动因素）。

### 5.3 个股详情页
- 标准 K 线图、分时图、日/周/月切换。
- 关键指标卡（市值/流通股/本周涨跌/换手率/PE 等）。
- 相关新闻与舆情时间轴（按时间排序，支持点击展开全文或来源）。
- AI简评（生成式，带数据引用）。

### 5.4 热点判定服务（核心业务）
- 每个板块按日（或分钟/小时）计算热度分数。
- 热度因子至少包含：成交额环比、量比、板块上涨家数比例、龙头占比涨幅、新闻/社交提及量、资金净流入、波动率上升。
- 支持基于规则或 ML 的权重设定（初期使用可配置规则）。
- 支持突发检测（如 CUSUM / Z-score）触发“新热点”告警。

### 5.5 LLM 服务（解释模块）
- 使用 RAG 流程：先检索（结构化行情数据 + 最近新闻）→ 生成回答。
- 回答必须包含：简短结论（1-3点）、每点至少一条数据依据（字段名+数值+时间）。
- 系统需校验生成回答中引用的数据是否与检索结果一致，否则标注“需复核”。

### 5.6 实时/缓存策略
- 行情与热度更新通过消息队列驱动，实时更新到缓存（Redis）并通过 WebSocket 推送给客户端。
- 常见页面请求优先读取缓存，缓存失效或用户强制刷新时回源。

### 5.7 用户与订阅
- 用户注册/登录（邮箱+密码；可选第三方登录）。
- 用户偏好设置（默认市场、关注板块/股票、告警阈值）。
- 告警（邮件/站内/推送）：板块热度超过阈值、龙头涨停/跌停等。

### 5.8 管理后台
- 数据源管理（行情/新闻/舆情接入配置）。
- Prompt 管理（LLM 模板）。
- 热点引擎参数配置（因子权重、阈值）。
- 数据监控（数据延迟、缺失告警）。


## 6 非功能性需求
- 性能：热点页面 P95 响应 < 500ms（缓存命中），实时流延迟 < 2s（行情）。
- 可用性：核心服务 SLA >= 99.9%（首年目标）。
- 可扩展性：支持水平扩展（高吞吐量行情）。
- 安全：TLS、KMS 加密敏感配置、审计日志。
- 可维护性：良好日志、指标与自动化部署。


## 7 数据需求与来源
### 7.1 必需数据类型
- 实时行情（tick、逐笔、K 线）
- 历史 K 线
- 资金流向（主力净流入/个股/板块）
- 板块映射（申万/自有）
- 新闻/公告/社交媒体（时间戳+来源）
- 基本面数据（财报、估值）

### 7.2 推荐数据供应商（示例）
- A 股：Wind、同花顺、东方财富（企业授权）
- 美股：IEX、Polygon、Tiingo
- 舆情：GDELT、新闻聚合 API、自建抓取 + Elasticsearch

### 7.3 合规要求
- 签署数据使用授权协议
- 对爬取数据遵循 robots 与源站政策


## 8 接口与数据模型（概要）
> 注：下游开发会基于此概要撰写 OpenAPI 文档。

### 8.1 关键接口（示例）
- GET /api/v1/market/overview
- GET /api/v1/hot-sectors?market=cn&limit=10
- GET /api/v1/sector/{id}/members?sort=change&limit=100
- GET /api/v1/stock/{symbol}/kline?range=30d
- POST /api/v1/llm/explain (body: context+question)
- WS /ws/market?token=...

### 8.2 数据模型（示例 JSON）
`sector_hotness` 表（示例）:
```json
{
  "sector_id": "semiconductor",
  "generated_at": "2025-09-06T09:30:12+08:00",
  "hotness": 78.2,
  "today_change": 3.2,
  "turnover": 1250000000,
  "top_stocks": [{"symbol":"0001","name":"芯片A","change":6.1,"volume_ratio":3.2}]
}
```


## 9 权限、合规与风控要求
- 在所有产生 AI 结论的页面显著显示“非投资建议”免责声明。
- 对 LLM 访问实行审计：记录 user_id、prompt、retrieval_context、模型返回、时间戳。
- 对敏感/违规查询（“如何操纵股价”）进行阻断与提示。
- 支付/订阅功能需合规实现（增值服务许可）。


## 10 验收标准与 KPI
- 功能验收：首页能正确显示当日 top10 热点（数据与后端一致）。
- 数据正确率：核心数据源在抽样检查中的一致性 >= 99%。
- LLM 输出一致性：抽样检查中，至少 80% 的生成结论与检索数据一致。
- 性能：热点页面 P95 响应 < 500ms，WebSocket 延迟 < 2s。


## 11 迭代计划与里程碑（建议）
- Sprint 0（Week0）：需求确认、数据供应商评估。
- Sprint 1（Week1-3）：基础数据管道与缓存，行情接入 PoC。
- Sprint 2（Week4-6）：热点判定引擎、API 与 Dashboard MVP。
- Sprint 3（Week7-9）：LLM 集成（RAG）、AI 卡片、后台管理。
- Sprint 4（Week10-12）：回测、告警、合规审查、Beta。


## 12 交付物清单
1. PRD（本文件）
2. API 设计（OpenAPI）
3. 数据字典 + ETL 流程图
4. 前端低/高保真设计稿（Figma）
5. LLM prompt 库与检索规则
6. 部署脚本、运维手册

---

# 设计文档（Detailed Design）

> 版本：1.0  
> 作者：系统架构师（ChatGPT 代写）  
> 日期：2025-09-06

## 目录
1. 总体架构概览
2. 子系统详细设计
   - 数据接入层
   - 数据处理与计算层
   - 存储层
   - 服务层（API / 热点引擎 / LLM）
   - 前端与交互层
   - 管理与运维
3. 数据模型与表设计
4. 热点判定算法详细设计
5. LLM 集成与 RAG 实现
6. API 设计（详细）
7. 安全设计
8. 可观测性与监控
9. 部署方案与扩展策略
10. 成本估算（粗略）
11. 测试计划

---

## 1 总体架构概览
系统采用微服务架构，按功能拆分：行情采集 -> 流处理 -> 时序存储/检索 -> 热点引擎 -> API 服务 -> 前端（React）/WebSocket。核心设计采用消息驱动（Kafka/ Pulsar）以保证高吞吐、解耦与重放能力。

主要组件：
- 行情采集器（Ingestors）
- 消息队列（Kafka 或 Pulsar）
- 流处理（Flink / ksql / 自研 worker）
- 时序 DB（ClickHouse / TimescaleDB）
- 文本索引（Elasticsearch / OpenSearch）
- 缓存（Redis）
- 后端服务（Go/Java/Python 微服务）
- LLM 服务（外部 API 或自托管模型 + Retrieval 服务）
- 前端（React + TradingView/ECharts）

架构图（文字说明）：
1. 数据源 -> 行情采集器 -> Kafka topic（raw_ticks, raw_kline, news）
2. 流处理 -> 计算实时聚合（sector turnover, volume ratio） -> 写入时序 DB + Redis
3. 检索索引（新闻/舆情）写入 Elastic
4. 热点引擎消费聚合结果，计算 hotness，写入 sector_hotness 表并发布到 ws topic
5. API 层从 Redis/DB 提供查询，LLM 服务调用检索结果并返回 explain


## 2 子系统详细设计
### 2.1 数据接入层
**职责**：稳定接入行情与文本数据，做基本校验与去重。

**实现要点**：
- 使用独立容器化采集器连接不同供应商的 websocket / REST / FTP 等。
- 将原始数据写入 Kafka（分 topic，按 market + symbol 分区）。
- 采集器自带回溯/断线重连与流量限流。

### 2.2 流处理与计算层
**职责**：实时计算因子（量比、成交额环比、板块内涨跌家数等）、生成热度分数并触发异常检测。

**实现要点**：
- 使用 Flink 或基于 Kafka Streams 的轻量实现。
- 窗口策略：1min、5min、15min、1day rolling window。
- 输出到 Redis（热数据）并周期写入时序 DB（历史存档）。

### 2.3 存储层
- **Redis**：缓存热点、用户会话、限流；使用 Redis Cluster。
- **TimescaleDB / ClickHouse**：存储高吞吐 K 线与聚合数据（ClickHouse 推荐用于深度分析和回测，Timescale 便于 SQL 时序）。
- **Elasticsearch**：存储新闻/社媒文档，支持全文检索与聚类。
- **S3**：存储导出报告、备份和大文件。

### 2.4 服务层
- **API Gateway**：鉴权、限流、统一日志。
- **Market Service**（Go）：行情聚合与历史 K 线接口。
- **Hotness Service**（Python/Go）：核心业务逻辑，提供 /hot-sectors 接口。
- **LLM Service**（Python）：负责构建检索上下文、调用模型并后处理。
- **User Service**：用户管理与订阅。
- **Admin Service**：后台管理接口。

### 2.5 前端与交互层
- **技术栈**：React + Vite + Tailwind。图表使用 TradingView Light 或 ECharts（K 线）
- **实时**：WebSocket + Reconnect 逻辑。
- **组件**：热点卡、板块详情、个股详情、AI 面板、告警设置、Prompt 控制台。

### 2.6 管理与运维
- CI/CD：GitHub Actions / GitLab CI -> k8s helm 部署。
- 配置管理：Vault/KMS 存储密钥与敏感配置。


## 3 数据模型与表设计（示例）
### 3.1 sector_hotness (ClickHouse / Timescale)
- sector_id (string)
- ts (timestamp)
- hotness (float)
- turnover (int64)
- n_up (int)
- n_down (int)
- top_stocks (json)
- factors (json) -- 每个因子值
- source_version (string)

### 3.2 stock_kline
- symbol, ts, open, high, low, close, volume, amount

### 3.3 news
- id, source, published_at, title, content, symbols[], sentiment_score, entities[]


## 4 热点判定算法详细设计
### 4.1 因子定义（每日/分钟）
- 成交额环比 (turnover_vs_1day_avg) = today_turnover / avg_turnover_last_5days
- 量比 (vol_ratio) = current_volume / avg_volume_same_period
- 上涨家数比例 (pct_up) = n_up / total_members
- 龙头贡献率 (leader_share) = sum(top3 turnovers) / sector_total_turnover
- 新闻热度 (news_count_norm) = normalized(news_count_last_6h)
- 资金流向 (fund_flow_norm) = normalized(net_inflow)
- 波动率上升 (volatility_delta) = volatility_7d - volatility_30d

所有因子归一化到 [0,1]，然后用可配置权重 w_i 加权求和：

`hotness = sigmoid( sum(w_i * factor_i) * scale + bias ) * 100`

sigmoid 是为了压缩到 0-100，可替换为线性缩放。

### 4.2 突发检测
- 使用短期热度差分与 CUSUM 检测，若 ∆hotness 在 10 分钟内 > threshold 则触发“突发”标签。

### 4.3 龙头选取逻辑
- 过滤掉小市值低流动性股票（市值 < threshold 或日均成交额 < threshold）
- 排序维度：当日涨幅 * alpha + 成交额占比 * beta + 换手率 * gamma
- 取 top N

### 4.4 回测思路
- 回测策略：在板块被标记为 hotness > X 时，以板块龙头在下一交易日开盘买入，持有 Y 天，记录收益。
- 指标：年化收益、夏普、最大回撤、胜率


## 5 LLM 集成与 RAG 实现
### 5.1 RAG 流程
1. User 请求 explain（板块/个股）
2. 系统检索：从时序 DB 获取近 7/30 日的关键指标，从 Elastic 获取近 48 小时相关新闻（top N）
3. 构造上下文：把结构化数据（简表）与新闻摘要一并传给 LLM（限制上下文长度）
4. LLM 生成回答，格式化输出（结论 / 依据列表 / 置信度）
5. 后处理：校验引用数据是否与检索数据一致；若不一致，标注需要复核。

### 5.2 Prompt 设计原则
- 明确 System 角色：禁止生成投资建议，要求给出数据依据。
- 提供数据表格而非生硬自然语言，让模型基于数据生成结论。
- 控制生成长度与温度（低温度较确定）。

### 5.3 模型部署选项
- 外部 API（OpenAI/Anthropic）: 快速上线，成本按调用计费。
- 自托管（Llama2、Mistral、Falcon）+向量 DB（Milvus/Weaviate）: 更便宜长期且可控，但需要运维与加速硬件。


## 6 API 设计（详细）
> 以下为部分核心接口示例（应做 OpenAPI 文档）

### 6.1 GET /api/v1/hot-sectors
参数：market, limit, min_hotness
返回：sector list（sector_id, name, hotness, today_change, turnover, top_stocks[], generated_at）

### 6.2 GET /api/v1/sector/{sector_id}/members
参数：sort (change|turnover|marketcap), page, page_size
返回：成员列表（含指标）

### 6.3 POST /api/v1/llm/explain
Body：{ context: {sector|stock, data: {...}}, question: string, user_id }
返回：{ answer: string, references: [{type, id, ts}], confidence: float }

### 6.4 WS /ws/market
鉴权后推送类型：ticker_update, sector_hotness_update, alert


## 7 安全设计
- 传输：TLS 1.2+（建议 1.3）
- 鉴权：OAuth2/JWT 或 API Key
- 敏感密钥：使用 Vault/KMS 存储
- 日志审计：记录 LLM 请求与检索上下文
- 权限隔离：管理后台与数据接入服务私有网络
- 输入校验：对用户可配置规则与 prompt 做白名单限制
- 滥用检测：对异常请求频次或敏感词触发阻断


## 8 可观测性与监控
- 指标：消息队列长度、处理延迟、DB 写入延迟、API 响应时间、LLM 调用成功率
- 日志：集中式 ELK 或 Loki
- 报警：Prometheus Alertmanager -> Slack / PagerDuty
- 仪表盘：Grafana（流处理、热点分布、用户活跃度）


## 9 部署方案与扩展策略
- 使用 Kubernetes（EKS / GKE / Self-host）部署微服务
- 使用 Helm 管理部署
- 存储：ClickHouse 集群 + S3 备份；Redis Cluster
- 弹性伸缩：基于 CPU/队列长度/延迟自动伸缩
- 灰度发布与 Canary 流量


## 10 成本估算（粗略）
- 行情数据：月度费用（取决于供应商）——可能占比最大
- 模型调用：按调用数量估算（外部 API 高）
- 基础设施：k8s 节点 + ClickHouse/Redis（中等规模，月度数千美元起）


## 11 测试计划
- 单元测试、集成测试覆盖核心计算逻辑（热度、因子）
- 数据一致性测试（采集->流->存储全链路）
- 性能测试：并发用户、WebSocket 吞吐、流处理延迟
- 安全测试：渗透测试、敏感词检测
- LLM 输出质量评估：人工抽样评价并生成质量评分


---

# 附录
- 推荐起手套件/技术栈：
  - 后端语言：Go（行情/高并发API）、Python（LLM、数据处理）
  - 消息队列：Kafka / Pulsar
  - 流式计算：Flink / Kafka Streams / ksqlDB
  - 存储：ClickHouse（分析）、Timescale（时序）、Redis（缓存）、Elastic（全文）
  - 前端：React + Vite + Tailwind + TradingView/ECharts
  - 向量数据库（若自建RAG）：Milvus / Weaviate


---

*说明：本文为产品经理与系统架构师级别的合成产出，已尽力覆盖业务、数据、系统和合规方面的细节。若要用于实际开发可继续转化为：
- 完整 OpenAPI（Swagger）文档
- 数据字典（每张表字段定义）
- 前端高保真设计稿（Figma）
- 热点引擎首版伪代码与回测脚本

如果你希望，我可以把以下项目中的任意一项“立刻输出”成开发可用产物（现在就生成，不异步）：
1. 完整 OpenAPI（Swagger）草案（所有核心接口与示例）
2. 热点判定引擎首版伪代码 + 回测脚本思路
3. 前端首页与板块页低保真 UI（静态 HTML/CSS 或 Figma 风格说明）
4. LLM prompt 模板库（10 个场景）及后处理校验逻辑

请选择一个我现在立刻生成，或告诉我需要对现有文档做哪些补充。  

