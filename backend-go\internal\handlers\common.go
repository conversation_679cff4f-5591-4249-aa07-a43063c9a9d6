package handlers

import (
	"ai-cg-backend/internal/models"
	"database/sql"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// SystemHealth 系统健康检查
// @Summary 系统健康检查
// @Description 检查系统和数据库健康状态
// @Tags 系统管理
// @Accept json
// @Produce json
// @Success 200 {object} models.HealthResponse
// @Failure 503 {object} models.ErrorResponse
// @Router /system/health [get]
func SystemHealth(db *sql.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查数据库连接
		if err := db.Ping(); err != nil {
			c.JSON(http.StatusServiceUnavailable, models.ErrorResponse{
				Error:   "database_unhealthy",
				Message: "数据库连接异常",
			})
			return
		}

		// 执行简单查询测试
		var result int
		err := db.QueryRow("SELECT 1").Scan(&result)
		if err != nil {
			c.JSON(http.StatusServiceUnavailable, models.ErrorResponse{
				Error:   "database_query_failed",
				Message: "数据库查询测试失败",
			})
			return
		}

		response := models.HealthResponse{
			Status:    "healthy",
			Service:   "AI炒股网站后端",
			Version:   "1.0.0",
			Timestamp: time.Now().Unix(),
			Details: map[string]interface{}{
				"database": "healthy",
				"uptime":   time.Now().Unix(),
			},
		}

		c.JSON(http.StatusOK, response)
	}
}

// SystemStats 系统统计
// @Summary 系统统计
// @Description 获取系统统计信息
// @Tags 系统管理
// @Accept json
// @Produce json
// @Success 200 {object} models.SystemStats
// @Failure 500 {object} models.ErrorResponse
// @Router /system/stats [get]
func SystemStats(db *sql.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		stats := models.SystemStats{}

		// 获取股票数量
		err := db.QueryRow("SELECT COUNT(*) FROM stocks WHERE is_active = 1").Scan(&stats.TotalStocks)
		if err != nil {
			stats.TotalStocks = 0
		}

		// 获取板块数量
		err = db.QueryRow("SELECT COUNT(*) FROM sectors WHERE is_active = 1").Scan(&stats.TotalSectors)
		if err != nil {
			stats.TotalSectors = 0
		}

		// 获取用户数量
		err = db.QueryRow("SELECT COUNT(*) FROM users WHERE is_active = 1").Scan(&stats.TotalUsers)
		if err != nil {
			stats.TotalUsers = 0
		}

		// 设置其他统计信息
		stats.ActiveConnections = 0           // TODO: 从WebSocket管理器获取
		stats.DataCollectorStatus = false     // TODO: 从服务状态获取
		stats.HotnessCalculatorStatus = false // TODO: 从服务状态获取
		stats.DatabaseSizeMB = 1.0            // TODO: 计算实际数据库大小

		now := time.Now().Format(time.RFC3339)
		stats.LastUpdate = &now

		c.JSON(http.StatusOK, stats)
	}
}

// GetStocks 获取股票列表
// @Summary 获取股票列表
// @Description 获取股票列表，支持按市场和板块筛选
// @Tags 股票数据
// @Accept json
// @Produce json
// @Param market query string false "市场代码" default(cn)
// @Param sector_id query string false "板块ID"
// @Param limit query int false "返回数量限制" default(100)
// @Param offset query int false "偏移量" default(0)
// @Success 200 {array} models.Stock
// @Failure 500 {object} models.ErrorResponse
// @Router /stocks [get]
func GetStocks(db *sql.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		market := c.DefaultQuery("market", "cn")
		sectorID := c.Query("sector_id")
		limit := parseIntParam(c, "limit", 100, 1, 500)
		offset := parseIntParam(c, "offset", 0, 0, 100000)

		// 构建WHERE条件
		whereClause := "WHERE is_active = 1"
		args := []interface{}{}

		if market != "all" {
			whereClause += " AND market = ?"
			args = append(args, market)
		}

		if sectorID != "" {
			whereClause += " AND sector_id = ?"
			args = append(args, sectorID)
		}

		query := fmt.Sprintf(`
			SELECT symbol, name, sector_id, market, list_date, market_cap, 
				   is_active, created_at, updated_at
			FROM stocks
			%s
			ORDER BY symbol
			LIMIT ? OFFSET ?
		`, whereClause)

		args = append(args, limit, offset)

		rows, err := db.Query(query, args...)
		if err != nil {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "database_error",
				Message: "获取股票列表失败",
			})
			return
		}
		defer rows.Close()

		var stocks []models.Stock
		for rows.Next() {
			var stock models.Stock
			err := rows.Scan(
				&stock.Symbol,
				&stock.Name,
				&stock.SectorID,
				&stock.Market,
				&stock.ListDate,
				&stock.MarketCap,
				&stock.IsActive,
				&stock.CreatedAt,
				&stock.UpdatedAt,
			)
			if err != nil {
				continue
			}
			stocks = append(stocks, stock)
		}

		c.JSON(http.StatusOK, stocks)
	}
}

// GetStockDetail 获取股票详情
// @Summary 获取股票详情
// @Description 获取指定股票的详细信息
// @Tags 股票数据
// @Accept json
// @Produce json
// @Param symbol path string true "股票代码"
// @Success 200 {object} map[string]interface{}
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /stocks/{symbol} [get]
func GetStockDetail(db *sql.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		symbol := c.Param("symbol")

		// 获取股票基本信息
		var stock models.Stock
		err := db.QueryRow(`
			SELECT symbol, name, sector_id, market, list_date, market_cap, 
				   is_active, created_at, updated_at
			FROM stocks 
			WHERE symbol = ? AND is_active = 1
		`, symbol).Scan(
			&stock.Symbol,
			&stock.Name,
			&stock.SectorID,
			&stock.Market,
			&stock.ListDate,
			&stock.MarketCap,
			&stock.IsActive,
			&stock.CreatedAt,
			&stock.UpdatedAt,
		)

		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "not_found",
				Message: "股票不存在",
			})
			return
		} else if err != nil {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "database_error",
				Message: "查询股票信息失败",
			})
			return
		}

		// 获取最新价格数据
		var latestPrice *models.StockKline
		err = db.QueryRow(`
			SELECT id, symbol, ts, open, high, low, close, volume, amount, period, created_at
			FROM stock_kline 
			WHERE symbol = ? AND period = '1d'
			ORDER BY ts DESC 
			LIMIT 1
		`, symbol).Scan(
			&latestPrice.ID,
			&latestPrice.Symbol,
			&latestPrice.Timestamp,
			&latestPrice.Open,
			&latestPrice.High,
			&latestPrice.Low,
			&latestPrice.Close,
			&latestPrice.Volume,
			&latestPrice.Amount,
			&latestPrice.Period,
			&latestPrice.CreatedAt,
		)

		var priceData interface{}
		if err == nil {
			priceData = latestPrice
		} else {
			priceData = nil
		}

		response := map[string]interface{}{
			"stock_info":   stock,
			"latest_price": priceData,
		}

		c.JSON(http.StatusOK, response)
	}
}

// GetStockKline 获取股票K线数据
// @Summary 获取股票K线数据
// @Description 获取指定股票的K线数据
// @Tags 股票数据
// @Accept json
// @Produce json
// @Param symbol path string true "股票代码"
// @Param period query string false "周期" default(1d)
// @Param days query int false "天数" default(30)
// @Success 200 {array} models.StockKline
// @Failure 500 {object} models.ErrorResponse
// @Router /stocks/{symbol}/kline [get]
func GetStockKline(db *sql.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		symbol := c.Param("symbol")
		period := c.DefaultQuery("period", "1d")
		days := parseIntParam(c, "days", 30, 1, 365)

		query := `
			SELECT id, symbol, ts, open, high, low, close, volume, amount, period, created_at
			FROM stock_kline
			WHERE symbol = ? AND period = ? AND ts >= datetime('now', '-? days')
			ORDER BY ts
		`

		rows, err := db.Query(query, symbol, period, days)
		if err != nil {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "database_error",
				Message: "获取K线数据失败",
			})
			return
		}
		defer rows.Close()

		var klines []models.StockKline
		for rows.Next() {
			var kline models.StockKline
			err := rows.Scan(
				&kline.ID,
				&kline.Symbol,
				&kline.Timestamp,
				&kline.Open,
				&kline.High,
				&kline.Low,
				&kline.Close,
				&kline.Volume,
				&kline.Amount,
				&kline.Period,
				&kline.CreatedAt,
			)
			if err != nil {
				continue
			}
			klines = append(klines, kline)
		}

		c.JSON(http.StatusOK, klines)
	}
}

// 辅助函数
func parseIntParam(c *gin.Context, param string, defaultValue, min, max int) int {
	if valueStr := c.Query(param); valueStr != "" {
		if value := parseInt(valueStr); value >= min && value <= max {
			return value
		}
	}
	return defaultValue
}

func parseInt(s string) int {
	if len(s) == 0 {
		return 0
	}
	value := 0
	for _, r := range s {
		if r >= '0' && r <= '9' {
			value = value*10 + int(r-'0')
		} else {
			return 0
		}
	}
	return value
}

