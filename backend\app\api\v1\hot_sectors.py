"""
热点板块API
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Optional
import asyncio
from datetime import datetime, timedelta

from app.core.database import db_manager
from app.models.schemas import HotSectorResponse, SectorDetailResponse, SectorMemberResponse
from app.services.hotness_calculator import HotnessCalculatorService
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.get("/", response_model=List[HotSectorResponse])
async def get_hot_sectors(
    market: str = Query("cn", description="市场代码 (cn/us)"),
    limit: int = Query(10, ge=1, le=50, description="返回数量限制"),
    min_hotness: float = Query(0.0, ge=0.0, le=100.0, description="最小热度阈值"),
    sort_by: str = Query("hotness", description="排序字段 (hotness/change/turnover)")
):
    """
    获取热点板块列表
    
    - **market**: 市场代码，cn=A股，us=美股
    - **limit**: 返回的板块数量限制
    - **min_hotness**: 最小热度阈值，0-100
    - **sort_by**: 排序字段
    """
    try:
        # 构建查询SQL
        sql = """
        SELECT 
            sh.sector_id,
            s.name,
            s.name_en,
            sh.hotness,
            sh.today_change,
            sh.turnover,
            sh.volume_ratio,
            sh.pct_up,
            sh.pct_down,
            sh.top_stocks,
            sh.factors,
            sh.ts as updated_at
        FROM sector_hotness sh
        JOIN sectors s ON sh.sector_id = s.id
        WHERE sh.hotness >= :min_hotness
        AND sh.ts >= :time_threshold
        ORDER BY sh.{sort_field} DESC
        LIMIT :limit
        """.format(sort_field=sort_by if sort_by in ['hotness', 'today_change', 'turnover'] else 'hotness')
        
        # 时间阈值：最近1小时的数据
        time_threshold = (datetime.now() - timedelta(hours=1)).isoformat()
        
        params = {
            "min_hotness": min_hotness,
            "time_threshold": time_threshold,
            "limit": limit
        }
        
        results = await db_manager.execute_raw_sql(sql, params)
        
        # 转换为响应模型
        hot_sectors = []
        for row in results:
            # 解析top_stocks JSON
            import json
            top_stocks = json.loads(row["top_stocks"]) if row["top_stocks"] else []
            factors = json.loads(row["factors"]) if row["factors"] else {}
            
            hot_sectors.append(HotSectorResponse(
                sector_id=row["sector_id"],
                name=row["name"],
                name_en=row["name_en"],
                hotness=row["hotness"],
                today_change=row["today_change"],
                turnover=row["turnover"],
                volume_ratio=row["volume_ratio"],
                pct_up=row["pct_up"],
                pct_down=row["pct_down"],
                top_stocks=top_stocks,
                factors=factors,
                updated_at=row["updated_at"]
            ))
        
        logger.info(f"返回 {len(hot_sectors)} 个热点板块")
        return hot_sectors
        
    except Exception as e:
        logger.error(f"获取热点板块失败: {e}")
        raise HTTPException(status_code=500, detail="获取热点板块失败")


@router.get("/{sector_id}", response_model=SectorDetailResponse)
async def get_sector_detail(sector_id: str):
    """
    获取板块详细信息
    
    - **sector_id**: 板块ID
    """
    try:
        # 获取板块基本信息
        sector_sql = """
        SELECT id, name, name_en, description, parent_id, industry_code
        FROM sectors 
        WHERE id = :sector_id AND is_active = 1
        """
        
        sector_info = await db_manager.execute_raw_sql_single(
            sector_sql, {"sector_id": sector_id}
        )
        
        if not sector_info:
            raise HTTPException(status_code=404, detail="板块不存在")
        
        # 获取最新热度信息
        hotness_sql = """
        SELECT hotness, today_change, turnover, volume_ratio, pct_up, pct_down, 
               top_stocks, factors, ts as updated_at
        FROM sector_hotness 
        WHERE sector_id = :sector_id
        ORDER BY ts DESC
        LIMIT 1
        """
        
        hotness_info = await db_manager.execute_raw_sql_single(
            hotness_sql, {"sector_id": sector_id}
        )
        
        # 获取板块成员
        members_sql = """
        SELECT sm.symbol, s.name, sm.weight, sm.is_leader,
               sk.close, sk.volume, sk.ts as last_update
        FROM sector_members sm
        JOIN stocks s ON sm.symbol = s.symbol
        LEFT JOIN (
            SELECT symbol, close, volume, ts,
                   ROW_NUMBER() OVER (PARTITION BY symbol ORDER BY ts DESC) as rn
            FROM stock_kline 
            WHERE period = '1d'
        ) sk ON sm.symbol = sk.symbol AND sk.rn = 1
        WHERE sm.sector_id = :sector_id
        ORDER BY sm.is_leader DESC, sm.weight DESC
        """
        
        members = await db_manager.execute_raw_sql(
            members_sql, {"sector_id": sector_id}
        )
        
        # 构建响应
        import json
        
        response = SectorDetailResponse(
            sector_id=sector_info["id"],
            name=sector_info["name"],
            name_en=sector_info["name_en"],
            description=sector_info["description"],
            parent_id=sector_info["parent_id"],
            industry_code=sector_info["industry_code"],
            hotness=hotness_info["hotness"] if hotness_info else 0.0,
            today_change=hotness_info["today_change"] if hotness_info else 0.0,
            turnover=hotness_info["turnover"] if hotness_info else 0,
            volume_ratio=hotness_info["volume_ratio"] if hotness_info else 0.0,
            pct_up=hotness_info["pct_up"] if hotness_info else 0.0,
            pct_down=hotness_info["pct_down"] if hotness_info else 0.0,
            factors=json.loads(hotness_info["factors"]) if hotness_info and hotness_info["factors"] else {},
            members=[
                SectorMemberResponse(
                    symbol=member["symbol"],
                    name=member["name"],
                    weight=member["weight"],
                    is_leader=member["is_leader"],
                    current_price=member["close"],
                    volume=member["volume"],
                    last_update=member["last_update"]
                ) for member in members
            ],
            updated_at=hotness_info["updated_at"] if hotness_info else None
        )
        
        logger.info(f"返回板块详情: {sector_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取板块详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取板块详情失败")


@router.get("/{sector_id}/members", response_model=List[SectorMemberResponse])
async def get_sector_members(
    sector_id: str,
    sort_by: str = Query("weight", description="排序字段"),
    order: str = Query("desc", description="排序方向 (asc/desc)"),
    limit: int = Query(100, ge=1, le=500, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """
    获取板块成员股票列表
    
    - **sector_id**: 板块ID
    - **sort_by**: 排序字段 (weight/price/volume/change)
    - **order**: 排序方向
    - **limit**: 返回数量限制
    - **offset**: 分页偏移量
    """
    try:
        # 验证排序字段
        valid_sort_fields = {
            "weight": "sm.weight",
            "price": "sk.close", 
            "volume": "sk.volume",
            "change": "(sk.close - sk.open) / sk.open * 100"
        }
        
        sort_field = valid_sort_fields.get(sort_by, "sm.weight")
        order_dir = "DESC" if order.lower() == "desc" else "ASC"
        
        sql = f"""
        SELECT sm.symbol, s.name, sm.weight, sm.is_leader,
               sk.open, sk.close, sk.high, sk.low, sk.volume, sk.amount,
               (sk.close - sk.open) / sk.open * 100 as change_pct,
               sk.ts as last_update
        FROM sector_members sm
        JOIN stocks s ON sm.symbol = s.symbol
        LEFT JOIN (
            SELECT symbol, open, close, high, low, volume, amount, ts,
                   ROW_NUMBER() OVER (PARTITION BY symbol ORDER BY ts DESC) as rn
            FROM stock_kline 
            WHERE period = '1d'
        ) sk ON sm.symbol = sk.symbol AND sk.rn = 1
        WHERE sm.sector_id = :sector_id
        ORDER BY {sort_field} {order_dir}
        LIMIT :limit OFFSET :offset
        """
        
        params = {
            "sector_id": sector_id,
            "limit": limit,
            "offset": offset
        }
        
        results = await db_manager.execute_raw_sql(sql, params)
        
        members = [
            SectorMemberResponse(
                symbol=row["symbol"],
                name=row["name"],
                weight=row["weight"],
                is_leader=row["is_leader"],
                current_price=row["close"],
                open_price=row["open"],
                high_price=row["high"],
                low_price=row["low"],
                volume=row["volume"],
                amount=row["amount"],
                change_pct=row["change_pct"],
                last_update=row["last_update"]
            ) for row in results
        ]
        
        logger.info(f"返回板块 {sector_id} 的 {len(members)} 个成员")
        return members
        
    except Exception as e:
        logger.error(f"获取板块成员失败: {e}")
        raise HTTPException(status_code=500, detail="获取板块成员失败")


@router.get("/{sector_id}/history")
async def get_sector_hotness_history(
    sector_id: str,
    days: int = Query(7, ge=1, le=365, description="历史天数"),
    interval: str = Query("1h", description="时间间隔 (1h/4h/1d)")
):
    """
    获取板块热度历史数据
    
    - **sector_id**: 板块ID
    - **days**: 历史天数
    - **interval**: 时间间隔
    """
    try:
        # 计算时间范围
        start_time = (datetime.now() - timedelta(days=days)).isoformat()
        
        # 根据interval确定时间分组
        time_format = {
            "1h": "%Y-%m-%d %H:00:00",
            "4h": "%Y-%m-%d %H:00:00", 
            "1d": "%Y-%m-%d 00:00:00"
        }.get(interval, "%Y-%m-%d %H:00:00")
        
        sql = """
        SELECT 
            strftime(:time_format, ts) as time_bucket,
            AVG(hotness) as avg_hotness,
            AVG(today_change) as avg_change,
            SUM(turnover) as total_turnover,
            AVG(volume_ratio) as avg_volume_ratio,
            COUNT(*) as data_points
        FROM sector_hotness
        WHERE sector_id = :sector_id 
        AND ts >= :start_time
        GROUP BY strftime(:time_format, ts)
        ORDER BY time_bucket
        """
        
        params = {
            "sector_id": sector_id,
            "start_time": start_time,
            "time_format": time_format
        }
        
        results = await db_manager.execute_raw_sql(sql, params)
        
        return {
            "sector_id": sector_id,
            "interval": interval,
            "days": days,
            "data": results
        }
        
    except Exception as e:
        logger.error(f"获取板块热度历史失败: {e}")
        raise HTTPException(status_code=500, detail="获取板块热度历史失败")


@router.post("/{sector_id}/refresh")
async def refresh_sector_hotness(sector_id: str):
    """
    手动刷新板块热度
    
    - **sector_id**: 板块ID
    """
    try:
        # 这里应该触发热度重新计算
        # 实际实现中会调用HotnessCalculatorService
        
        # 验证板块存在
        sector_sql = "SELECT id FROM sectors WHERE id = :sector_id AND is_active = 1"
        sector = await db_manager.execute_raw_sql_single(
            sector_sql, {"sector_id": sector_id}
        )
        
        if not sector:
            raise HTTPException(status_code=404, detail="板块不存在")
        
        # TODO: 触发热度重新计算
        # await hotness_calculator.calculate_sector_hotness(sector_id)
        
        return {
            "message": f"板块 {sector_id} 热度刷新请求已提交",
            "sector_id": sector_id,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刷新板块热度失败: {e}")
        raise HTTPException(status_code=500, detail="刷新板块热度失败")

