"""
系统管理API
"""

from fastapi import APIRouter, HTTPException
from app.core.database import db_manager, check_database_health
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.get("/health")
async def system_health():
    """系统健康检查"""
    try:
        db_health = await check_database_health()
        
        return {
            "status": "healthy",
            "database": db_health,
            "timestamp": "2024-01-01T00:00:00"
        }
    except Exception as e:
        logger.error(f"系统健康检查失败: {e}")
        raise HTTPException(status_code=503, detail="系统不健康")


@router.get("/stats")
async def system_stats():
    """系统统计信息"""
    try:
        # 统计各类数据
        stats = {}
        
        # 股票数量
        result = await db_manager.execute_raw_sql_single(
            "SELECT COUNT(*) as count FROM stocks WHERE is_active = 1"
        )
        stats["total_stocks"] = result["count"] if result else 0
        
        # 板块数量
        result = await db_manager.execute_raw_sql_single(
            "SELECT COUNT(*) as count FROM sectors WHERE is_active = 1"
        )
        stats["total_sectors"] = result["count"] if result else 0
        
        # 用户数量
        result = await db_manager.execute_raw_sql_single(
            "SELECT COUNT(*) as count FROM users WHERE is_active = 1"
        )
        stats["total_users"] = result["count"] if result else 0
        
        return stats
        
    except Exception as e:
        logger.error(f"获取系统统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取系统统计失败")

