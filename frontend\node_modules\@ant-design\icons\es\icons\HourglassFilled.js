import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import HourglassFilledSvg from "@ant-design/icons-svg/es/asn/HourglassFilled";
import AntdIcon from "../components/AntdIcon";
var HourglassFilled = function HourglassFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: HourglassFilledSvg
  }));
};

/**![hourglass](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc0MiAzMThWMTg0aDg2YzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04SDE5NmMtNC40IDAtOCAzLjYtOCA4djU2YzAgNC40IDMuNiA4IDggOGg4NnYxMzRjMCA4MS41IDQyLjQgMTUzLjIgMTA2LjQgMTk0LTY0IDQwLjgtMTA2LjQgMTEyLjUtMTA2LjQgMTk0djEzNGgtODZjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDhoNjMyYzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04aC04NlY3MDZjMC04MS41LTQyLjQtMTUzLjItMTA2LjQtMTk0IDY0LTQwLjggMTA2LjQtMTEyLjUgMTA2LjQtMTk0eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(HourglassFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'HourglassFilled';
}
export default RefIcon;