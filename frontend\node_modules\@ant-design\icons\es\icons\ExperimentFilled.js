import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ExperimentFilledSvg from "@ant-design/icons-svg/es/asn/ExperimentFilled";
import AntdIcon from "../components/AntdIcon";
var ExperimentFilled = function ExperimentFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: ExperimentFilledSvg
  }));
};

/**![experiment](data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */
var RefIcon = /*#__PURE__*/React.forwardRef(ExperimentFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ExperimentFilled';
}
export default RefIcon;