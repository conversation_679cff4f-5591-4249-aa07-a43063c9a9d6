"""
LLM服务
集成大语言模型，提供智能分析和问答功能
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any
import logging
from datetime import datetime

import openai
from app.core.config import settings
from app.core.database import db_manager
from app.utils.logger import get_logger

logger = get_logger(__name__)


class LLMService:
    """LLM服务类"""
    
    def __init__(self):
        self.client = None
        self.is_available = False
        self._initialize_client()
        
    def _initialize_client(self):
        """初始化OpenAI客户端"""
        try:
            if settings.OPENAI_API_KEY:
                openai.api_key = settings.OPENAI_API_KEY
                openai.base_url = settings.OPENAI_BASE_URL
                self.client = openai
                self.is_available = True
                logger.info("✅ OpenAI客户端初始化成功")
            else:
                logger.warning("⚠️ 未配置OPENAI_API_KEY，LLM功能不可用")
        except Exception as e:
            logger.error(f"❌ OpenAI客户端初始化失败: {e}")
            self.is_available = False
    
    async def ask_question(
        self,
        question: str,
        context_type: str = "general",
        context_id: Optional[str] = None,
        user_id: Optional[int] = None,
        model_name: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        向LLM提问
        
        Args:
            question: 用户问题
            context_type: 上下文类型 (sector, stock, market, general)
            context_id: 上下文ID (板块ID或股票代码)
            user_id: 用户ID
            model_name: 模型名称
            max_tokens: 最大token数
            temperature: 温度参数
        """
        if not self.is_available:
            return {
                "answer": "抱歉，AI服务暂时不可用。请稍后重试。",
                "error": "LLM service not available",
                "references": [],
                "confidence": 0.0
            }
        
        start_time = time.time()
        
        try:
            # 构建上下文
            context_data = await self._build_context(context_type, context_id)
            
            # 构建提示词
            prompt = await self._build_prompt(question, context_type, context_data)
            
            # 调用LLM
            response = await self._call_llm(
                prompt=prompt,
                model_name=model_name or settings.OPENAI_MODEL,
                max_tokens=max_tokens or settings.OPENAI_MAX_TOKENS,
                temperature=temperature or settings.OPENAI_TEMPERATURE
            )
            
            # 处理响应
            result = await self._process_response(response, context_data)
            
            # 记录日志
            processing_time = int((time.time() - start_time) * 1000)
            await self._log_request(
                user_id=user_id,
                question=question,
                context_type=context_type,
                context_id=context_id,
                context_data=context_data,
                response=result,
                processing_time=processing_time,
                model_name=model_name or settings.OPENAI_MODEL
            )
            
            result["processing_time_ms"] = processing_time
            result["model_name"] = model_name or settings.OPENAI_MODEL
            
            return result
            
        except Exception as e:
            logger.error(f"LLM问答失败: {e}")
            return {
                "answer": f"抱歉，处理您的问题时出现错误：{str(e)}",
                "error": str(e),
                "references": [],
                "confidence": 0.0,
                "processing_time_ms": int((time.time() - start_time) * 1000)
            }
    
    async def _build_context(self, context_type: str, context_id: Optional[str]) -> Dict[str, Any]:
        """构建上下文数据"""
        context = {"type": context_type}
        
        try:
            if context_type == "sector" and context_id:
                # 获取板块信息
                sector_sql = """
                SELECT s.*, sh.hotness, sh.today_change, sh.turnover, sh.factors
                FROM sectors s
                LEFT JOIN sector_hotness sh ON s.id = sh.sector_id
                WHERE s.id = :sector_id
                ORDER BY sh.ts DESC
                LIMIT 1
                """
                sector_info = await db_manager.execute_raw_sql_single(
                    sector_sql, {"sector_id": context_id}
                )
                
                if sector_info:
                    context["sector"] = sector_info
                    
                    # 获取板块成员股票
                    members_sql = """
                    SELECT sm.symbol, s.name, sm.weight, sm.is_leader,
                           sk.close, sk.volume, (sk.close - sk.open) / sk.open * 100 as change_pct
                    FROM sector_members sm
                    JOIN stocks s ON sm.symbol = s.symbol
                    LEFT JOIN (
                        SELECT symbol, open, close, volume,
                               ROW_NUMBER() OVER (PARTITION BY symbol ORDER BY ts DESC) as rn
                        FROM stock_kline WHERE period = '1d'
                    ) sk ON sm.symbol = sk.symbol AND sk.rn = 1
                    WHERE sm.sector_id = :sector_id
                    ORDER BY sm.is_leader DESC, sm.weight DESC
                    LIMIT 10
                    """
                    members = await db_manager.execute_raw_sql(
                        members_sql, {"sector_id": context_id}
                    )
                    context["members"] = members
                    
            elif context_type == "stock" and context_id:
                # 获取股票信息
                stock_sql = """
                SELECT s.*, sk.open, sk.close, sk.high, sk.low, sk.volume
                FROM stocks s
                LEFT JOIN (
                    SELECT symbol, open, close, high, low, volume,
                           ROW_NUMBER() OVER (PARTITION BY symbol ORDER BY ts DESC) as rn
                    FROM stock_kline WHERE period = '1d'
                ) sk ON s.symbol = sk.symbol AND sk.rn = 1
                WHERE s.symbol = :symbol
                """
                stock_info = await db_manager.execute_raw_sql_single(
                    stock_sql, {"symbol": context_id}
                )
                
                if stock_info:
                    context["stock"] = stock_info
                    
            elif context_type == "market":
                # 获取市场概况
                market_sql = """
                SELECT 
                    COUNT(*) as total_stocks,
                    SUM(CASE WHEN sk.close > sk.open THEN 1 ELSE 0 END) as up_stocks,
                    SUM(CASE WHEN sk.close < sk.open THEN 1 ELSE 0 END) as down_stocks,
                    AVG((sk.close - sk.open) / sk.open * 100) as avg_change
                FROM stocks s
                JOIN (
                    SELECT symbol, open, close,
                           ROW_NUMBER() OVER (PARTITION BY symbol ORDER BY ts DESC) as rn
                    FROM stock_kline WHERE period = '1d'
                ) sk ON s.symbol = sk.symbol AND sk.rn = 1
                WHERE s.is_active = 1
                """
                market_info = await db_manager.execute_raw_sql_single(market_sql)
                context["market"] = market_info
                
                # 获取热门板块
                hot_sectors_sql = """
                SELECT sector_id, hotness, today_change, turnover
                FROM sector_hotness
                WHERE ts >= datetime('now', '-1 hour')
                ORDER BY hotness DESC
                LIMIT 5
                """
                hot_sectors = await db_manager.execute_raw_sql(hot_sectors_sql)
                context["hot_sectors"] = hot_sectors
            
            # 获取相关新闻
            if context_id:
                news_sql = """
                SELECT title, published_at, sentiment_score
                FROM news
                WHERE (symbols LIKE :context_pattern OR symbols LIKE :context_id)
                AND published_at >= datetime('now', '-24 hours')
                ORDER BY published_at DESC
                LIMIT 5
                """
                news = await db_manager.execute_raw_sql(
                    news_sql, {
                        "context_pattern": f"%{context_id}%",
                        "context_id": context_id
                    }
                )
                context["related_news"] = news
                
        except Exception as e:
            logger.error(f"构建上下文失败: {e}")
            
        return context
    
    async def _build_prompt(self, question: str, context_type: str, context_data: Dict) -> str:
        """构建提示词"""
        
        system_prompt = """你是一个专业的股票市场分析师，具有丰富的A股市场经验。请基于提供的数据回答用户问题。

重要要求：
1. 回答必须基于提供的数据，不要编造信息
2. 如果数据不足，请明确说明
3. 不要提供具体的投资建议，只提供客观分析
4. 回答要简洁明了，重点突出
5. 必须包含免责声明

数据说明：
- hotness: 热度分数(0-100)，越高越热门
- today_change: 今日涨跌幅(%)
- turnover: 成交额(元)
- volume: 成交量
- change_pct: 涨跌幅(%)
"""
        
        # 根据上下文类型添加特定信息
        context_info = ""
        
        if context_type == "sector" and "sector" in context_data:
            sector = context_data["sector"]
            members = context_data.get("members", [])
            
            context_info = f"""
当前分析板块：{sector.get('name', 'N/A')}
- 热度分数：{sector.get('hotness', 0):.1f}
- 今日涨跌：{sector.get('today_change', 0):.2f}%
- 成交额：{sector.get('turnover', 0):,}元

主要成员股票：
"""
            for member in members[:5]:
                context_info += f"- {member['name']}({member['symbol']})：{member.get('change_pct', 0):.2f}%\n"
                
        elif context_type == "market" and "market" in context_data:
            market = context_data["market"]
            hot_sectors = context_data.get("hot_sectors", [])
            
            context_info = f"""
市场整体情况：
- 总股票数：{market.get('total_stocks', 0)}
- 上涨股票：{market.get('up_stocks', 0)}
- 下跌股票：{market.get('down_stocks', 0)}
- 平均涨跌幅：{market.get('avg_change', 0):.2f}%

热门板块：
"""
            for sector in hot_sectors:
                context_info += f"- {sector['sector_id']}：热度{sector['hotness']:.1f}，涨跌{sector.get('today_change', 0):.2f}%\n"
        
        # 添加相关新闻
        if "related_news" in context_data and context_data["related_news"]:
            context_info += "\n相关新闻：\n"
            for news in context_data["related_news"][:3]:
                context_info += f"- {news['title']} (情感分数: {news.get('sentiment_score', 0):.2f})\n"
        
        prompt = f"""{system_prompt}

{context_info}

用户问题：{question}

请基于以上数据进行分析回答。"""
        
        return prompt
    
    async def _call_llm(self, prompt: str, model_name: str, max_tokens: int, temperature: float) -> Dict:
        """调用LLM API"""
        try:
            response = await asyncio.to_thread(
                openai.chat.completions.create,
                model=model_name,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=max_tokens,
                temperature=temperature
            )
            
            return {
                "content": response.choices[0].message.content,
                "usage": response.usage.model_dump() if response.usage else None,
                "model": response.model
            }
            
        except Exception as e:
            logger.error(f"调用LLM API失败: {e}")
            raise
    
    async def _process_response(self, response: Dict, context_data: Dict) -> Dict[str, Any]:
        """处理LLM响应"""
        content = response.get("content", "")
        usage = response.get("usage", {})
        
        # 提取数据引用
        references = []
        if context_data:
            # 简化的引用提取，实际可以更复杂
            if "sector" in context_data:
                references.append({
                    "type": "sector_data",
                    "id": context_data["sector"].get("id"),
                    "name": context_data["sector"].get("name")
                })
            if "market" in context_data:
                references.append({
                    "type": "market_data",
                    "timestamp": datetime.now().isoformat()
                })
        
        # 计算置信度（简化版本）
        confidence = 0.8 if len(content) > 50 else 0.5
        
        return {
            "answer": content,
            "references": references,
            "confidence": confidence,
            "token_usage": usage.get("total_tokens", 0),
            "error": None
        }
    
    async def _log_request(
        self,
        user_id: Optional[int],
        question: str,
        context_type: str,
        context_id: Optional[str],
        context_data: Dict,
        response: Dict,
        processing_time: int,
        model_name: str
    ):
        """记录LLM请求日志"""
        try:
            sql = """
            INSERT INTO llm_request_logs 
            (user_id, request_type, prompt, context_data, model_name, response, 
             token_usage, processing_time_ms, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = [
                user_id,
                context_type,
                question,
                json.dumps(context_data, ensure_ascii=False),
                model_name,
                response.get("answer", ""),
                response.get("token_usage", 0),
                processing_time,
                datetime.now().isoformat()
            ]
            
            await db_manager.execute_insert(sql, params)
            
        except Exception as e:
            logger.error(f"记录LLM请求日志失败: {e}")
    
    async def get_usage_stats(self) -> Dict[str, Any]:
        """获取LLM使用统计"""
        try:
            stats_sql = """
            SELECT 
                COUNT(*) as total_requests,
                AVG(token_usage) as avg_tokens,
                AVG(processing_time_ms) as avg_processing_time,
                DATE(created_at) as date
            FROM llm_request_logs
            WHERE created_at >= datetime('now', '-7 days')
            GROUP BY DATE(created_at)
            ORDER BY date DESC
            """
            
            daily_stats = await db_manager.execute_raw_sql(stats_sql)
            
            total_sql = """
            SELECT 
                COUNT(*) as total_requests,
                SUM(token_usage) as total_tokens,
                COUNT(DISTINCT user_id) as unique_users
            FROM llm_request_logs
            WHERE created_at >= datetime('now', '-30 days')
            """
            
            total_stats = await db_manager.execute_raw_sql_single(total_sql)
            
            return {
                "daily_stats": daily_stats,
                "total_stats": total_stats,
                "is_available": self.is_available
            }
            
        except Exception as e:
            logger.error(f"获取LLM使用统计失败: {e}")
            return {
                "daily_stats": [],
                "total_stats": {},
                "is_available": self.is_available,
                "error": str(e)
            }


# 全局LLM服务实例
llm_service = LLMService()

