import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FundProjectionScreenOutlinedSvg from "@ant-design/icons-svg/es/asn/FundProjectionScreenOutlined";
import AntdIcon from "../components/AntdIcon";
var FundProjectionScreenOutlined = function FundProjectionScreenOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: FundProjectionScreenOutlinedSvg
  }));
};

/**![fund-projection-screen](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik0zMTIuMSA1OTEuNWMzLjEgMy4xIDguMiAzLjEgMTEuMyAwbDEwMS44LTEwMS44IDg2LjEgODYuMmMzLjEgMy4xIDguMiAzLjEgMTEuMyAwbDIyNi4zLTIyNi41YzMuMS0zLjEgMy4xLTguMiAwLTExLjNsLTM2LjgtMzYuOGE4LjAzIDguMDMgMCAwMC0xMS4zIDBMNTE3IDQ4NS4zbC04Ni4xLTg2LjJhOC4wMyA4LjAzIDAgMDAtMTEuMyAwTDI3NS4zIDU0My40YTguMDMgOC4wMyAwIDAwMCAxMS4zbDM2LjggMzYuOHoiIC8+PHBhdGggZD0iTTkwNCAxNjBINTQ4Vjk2YzAtNC40LTMuNi04LTgtOGgtNTZjLTQuNCAwLTggMy42LTggOHY2NEgxMjBjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjUyMGMwIDE3LjcgMTQuMyAzMiAzMiAzMmgzNTYuNHYzMkwzMTEuNiA4ODQuMWE3LjkyIDcuOTIgMCAwMC0yLjMgMTFsMzAuMyA0Ny4ydi4xYzIuNCAzLjcgNy40IDQuNyAxMS4xIDIuM0w1MTIgODM4LjlsMTYxLjMgMTA1LjhjMy43IDIuNCA4LjcgMS40IDExLjEtMi4zdi0uMWwzMC4zLTQ3LjJhOCA4IDAgMDAtMi4zLTExTDU0OCA3NzYuM1Y3NDRoMzU2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE5MmMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDUxMkgxNjBWMjMyaDcwNHY0NDB6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(FundProjectionScreenOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FundProjectionScreenOutlined';
}
export default RefIcon;