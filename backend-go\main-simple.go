package main

import (
	"ai-cg-backend/internal/config"
	"ai-cg-backend/internal/database"
	"ai-cg-backend/internal/handlers"
	"log"
	"net/http"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 初始化数据库
	db, err := database.Init(cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}
	defer db.Close()

	// 创建Gin引擎
	router := gin.Default()

	// CORS配置
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowOrigins = []string{"http://localhost:3000", "http://localhost:3001"}
	corsConfig.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	corsConfig.AllowHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization"}
	corsConfig.AllowCredentials = true
	router.Use(cors.New(corsConfig))

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "healthy",
			"service": "AI炒股网站Go后端",
			"version": "1.0.0",
		})
	})

	// API路由组
	v1 := router.Group("/api/v1")
	{
		// 热点板块相关路由
		hotSectors := v1.Group("/hot-sectors")
		{
			hotSectors.GET("", handlers.GetHotSectors(db))
			hotSectors.GET("/:sectorId", handlers.GetSectorDetail(db))
		}

		// 系统相关路由
		system := v1.Group("/system")
		{
			system.GET("/health", handlers.SystemHealth(db))
			system.GET("/stats", handlers.SystemStats(db))
		}

		// LLM相关路由
		llm := v1.Group("/llm")
		{
			llm.POST("/ask", handlers.AskLLM(db))
			llm.GET("/health", handlers.LLMHealth())
		}
	}

	log.Printf("🚀 Go后端服务启动在端口 %s", cfg.Port)
	log.Printf("🔍 健康检查: http://localhost:%s/health", cfg.Port)

	// 启动服务器
	if err := router.Run(":" + cfg.Port); err != nil {
		log.Fatalf("服务器启动失败: %v", err)
	}
}

