"""
WebSocket连接管理器
"""

import asyncio
import json
from typing import Dict, List, Set
from fastapi import WebSocket
from app.utils.logger import get_logger

logger = get_logger(__name__)


class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.subscriptions: Dict[WebSocket, Set[str]] = {}
        
    async def connect(self, websocket: WebSocket):
        """接受新的WebSocket连接"""
        await websocket.accept()
        self.active_connections.append(websocket)
        self.subscriptions[websocket] = set()
        logger.info(f"新的WebSocket连接，当前连接数: {len(self.active_connections)}")
        
    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if websocket in self.subscriptions:
            del self.subscriptions[websocket]
        logger.info(f"WebSocket连接断开，当前连接数: {len(self.active_connections)}")
        
    async def subscribe(self, websocket: WebSocket, channels: List[str]):
        """订阅频道"""
        if websocket in self.subscriptions:
            self.subscriptions[websocket].update(channels)
            logger.info(f"WebSocket订阅频道: {channels}")
            
    async def unsubscribe(self, websocket: WebSocket, channels: List[str]):
        """取消订阅频道"""
        if websocket in self.subscriptions:
            self.subscriptions[websocket].difference_update(channels)
            logger.info(f"WebSocket取消订阅频道: {channels}")
            
    async def broadcast(self, message: dict, channel: str = None):
        """广播消息"""
        message_text = json.dumps(message)
        disconnected = []
        
        for websocket in self.active_connections:
            try:
                # 如果指定了频道，检查订阅
                if channel and websocket in self.subscriptions:
                    if channel not in self.subscriptions[websocket]:
                        continue
                        
                await websocket.send_text(message_text)
            except Exception as e:
                logger.error(f"发送WebSocket消息失败: {e}")
                disconnected.append(websocket)
                
        # 清理断开的连接
        for websocket in disconnected:
            self.disconnect(websocket)
            
    async def send_to_user(self, websocket: WebSocket, message: dict):
        """发送消息给特定用户"""
        try:
            message_text = json.dumps(message)
            await websocket.send_text(message_text)
        except Exception as e:
            logger.error(f"发送WebSocket消息失败: {e}")
            self.disconnect(websocket)

