"""
数据库连接和会话管理
"""

import asyncio
import aiosqlite
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import declarative_base
from sqlalchemy import MetaData
from contextlib import asynccontextmanager
from typing import AsyncGenerator
import os
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# 数据库元数据
metadata = MetaData()
Base = declarative_base(metadata=metadata)

# 异步数据库引擎
engine = None
AsyncSessionLocal = None


async def init_db():
    """初始化数据库"""
    global engine, AsyncSessionLocal
    
    try:
        # 确保数据目录存在
        db_path = settings.DATABASE_URL.replace("sqlite:///", "")
        db_dir = os.path.dirname(db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)
        
        # 创建异步引擎
        engine = create_async_engine(
            settings.DATABASE_URL.replace("sqlite:///", "sqlite+aiosqlite:///"),
            echo=settings.DATABASE_ECHO,
            future=True,
            pool_pre_ping=True,
        )
        
        # 创建会话工厂
        AsyncSessionLocal = async_sessionmaker(
            engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        # 初始化数据库结构
        await init_database_schema()
        
        logger.info("✅ 数据库初始化成功")
        
    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        raise


async def init_database_schema():
    """初始化数据库结构"""
    try:
        # 读取并执行初始化SQL脚本
        init_sql_path = "database/init.sql"
        if os.path.exists(init_sql_path):
            with open(init_sql_path, "r", encoding="utf-8") as f:
                init_sql = f.read()
            
            # 使用原生SQLite连接执行初始化脚本
            db_path = settings.DATABASE_URL.replace("sqlite:///", "")
            async with aiosqlite.connect(db_path) as db:
                await db.executescript(init_sql)
                await db.commit()
            
            logger.info("✅ 数据库结构初始化完成")
        else:
            logger.warning("⚠️ 未找到数据库初始化脚本")
            
    except Exception as e:
        logger.error(f"❌ 数据库结构初始化失败: {e}")
        raise


@asynccontextmanager
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话"""
    if AsyncSessionLocal is None:
        raise RuntimeError("数据库未初始化，请先调用 init_db()")
    
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error(f"数据库会话错误: {e}")
            raise
        finally:
            await session.close()


async def get_db():
    """FastAPI依赖注入用的数据库会话"""
    async with get_db_session() as session:
        yield session


class DatabaseManager:
    """数据库管理类"""
    
    def __init__(self):
        self.engine = engine
        self.session_factory = AsyncSessionLocal
    
    async def execute_raw_sql(self, sql: str, params: dict = None) -> list:
        """执行原始SQL查询"""
        try:
            db_path = settings.DATABASE_URL.replace("sqlite:///", "")
            async with aiosqlite.connect(db_path) as db:
                db.row_factory = aiosqlite.Row  # 返回字典格式
                if params:
                    cursor = await db.execute(sql, params)
                else:
                    cursor = await db.execute(sql)
                rows = await cursor.fetchall()
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"执行SQL失败: {sql}, 错误: {e}")
            raise
    
    async def execute_raw_sql_single(self, sql: str, params: dict = None) -> dict:
        """执行原始SQL查询并返回单条记录"""
        results = await self.execute_raw_sql(sql, params)
        return results[0] if results else None
    
    async def execute_insert(self, sql: str, params: dict = None) -> int:
        """执行INSERT语句并返回lastrowid"""
        try:
            db_path = settings.DATABASE_URL.replace("sqlite:///", "")
            async with aiosqlite.connect(db_path) as db:
                if params:
                    cursor = await db.execute(sql, params)
                else:
                    cursor = await db.execute(sql)
                await db.commit()
                return cursor.lastrowid
        except Exception as e:
            logger.error(f"执行INSERT失败: {sql}, 错误: {e}")
            raise
    
    async def execute_update(self, sql: str, params: dict = None) -> int:
        """执行UPDATE/DELETE语句并返回影响行数"""
        try:
            db_path = settings.DATABASE_URL.replace("sqlite:///", "")
            async with aiosqlite.connect(db_path) as db:
                if params:
                    cursor = await db.execute(sql, params)
                else:
                    cursor = await db.execute(sql)
                await db.commit()
                return cursor.rowcount
        except Exception as e:
            logger.error(f"执行UPDATE失败: {sql}, 错误: {e}")
            raise
    
    async def backup_database(self, backup_path: str = None):
        """备份数据库"""
        try:
            if not backup_path:
                import datetime
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"{settings.BACKUP_DIR}/backup_{timestamp}.db"
            
            # 确保备份目录存在
            backup_dir = os.path.dirname(backup_path)
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir, exist_ok=True)
            
            # 复制数据库文件
            db_path = settings.DATABASE_URL.replace("sqlite:///", "")
            import shutil
            shutil.copy2(db_path, backup_path)
            
            logger.info(f"✅ 数据库备份完成: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"❌ 数据库备份失败: {e}")
            raise
    
    async def get_table_info(self, table_name: str) -> list:
        """获取表结构信息"""
        sql = f"PRAGMA table_info({table_name})"
        return await self.execute_raw_sql(sql)
    
    async def get_all_tables(self) -> list:
        """获取所有表名"""
        sql = "SELECT name FROM sqlite_master WHERE type='table'"
        results = await self.execute_raw_sql(sql)
        return [row["name"] for row in results]
    
    async def vacuum_database(self):
        """整理数据库，回收空间"""
        try:
            db_path = settings.DATABASE_URL.replace("sqlite:///", "")
            async with aiosqlite.connect(db_path) as db:
                await db.execute("VACUUM")
                await db.commit()
            logger.info("✅ 数据库整理完成")
        except Exception as e:
            logger.error(f"❌ 数据库整理失败: {e}")
            raise


# 创建全局数据库管理器实例
db_manager = DatabaseManager()


async def close_db():
    """关闭数据库连接"""
    global engine
    if engine:
        await engine.dispose()
        logger.info("✅ 数据库连接已关闭")


# 数据库健康检查
async def check_database_health() -> dict:
    """检查数据库健康状态"""
    try:
        # 测试连接
        result = await db_manager.execute_raw_sql("SELECT 1 as test")
        
        # 获取数据库大小
        db_path = settings.DATABASE_URL.replace("sqlite:///", "")
        db_size = os.path.getsize(db_path) if os.path.exists(db_path) else 0
        
        # 获取表数量
        tables = await db_manager.get_all_tables()
        
        return {
            "status": "healthy",
            "connection": "ok",
            "database_size_bytes": db_size,
            "database_size_mb": round(db_size / 1024 / 1024, 2),
            "table_count": len(tables),
            "tables": tables
        }
        
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }

