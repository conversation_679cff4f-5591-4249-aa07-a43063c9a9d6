import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FastBackwardFilledSvg from "@ant-design/icons-svg/es/asn/FastBackwardFilled";
import AntdIcon from "../components/AntdIcon";
var FastBackwardFilled = function FastBackwardFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: FastBackwardFilledSvg
  }));
};

/**![fast-backward](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxNy42IDI3My41TDIzMC4yIDQ5OS4zYTE2LjE0IDE2LjE0IDAgMDAwIDI1LjRsMjg3LjQgMjI1LjhjMTAuNyA4LjQgMjYuNC44IDI2LjQtMTIuN1YyODYuMmMwLTEzLjUtMTUuNy0yMS4xLTI2LjQtMTIuN3ptMzIwIDBMNTUwLjIgNDk5LjNhMTYuMTQgMTYuMTQgMCAwMDAgMjUuNGwyODcuNCAyMjUuOGMxMC43IDguNCAyNi40LjggMjYuNC0xMi43VjI4Ni4yYzAtMTMuNS0xNS43LTIxLjEtMjYuNC0xMi43em0tNjIwLTI1LjVoLTUxLjJjLTMuNSAwLTYuNCAyLjctNi40IDZ2NTE2YzAgMy4zIDIuOSA2IDYuNCA2aDUxLjJjMy41IDAgNi40LTIuNyA2LjQtNlYyNTRjMC0zLjMtMi45LTYtNi40LTZ6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(FastBackwardFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FastBackwardFilled';
}
export default RefIcon;