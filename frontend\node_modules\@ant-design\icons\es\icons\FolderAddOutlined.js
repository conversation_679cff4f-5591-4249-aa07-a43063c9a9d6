import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FolderAddOutlinedSvg from "@ant-design/icons-svg/es/asn/FolderAddOutlined";
import AntdIcon from "../components/AntdIcon";
var FolderAddOutlined = function FolderAddOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: FolderAddOutlinedSvg
  }));
};

/**![folder-add](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ4NCA0NDMuMVY1MjhoLTg0LjVjLTQuMSAwLTcuNSAzLjEtNy41IDd2NDJjMCAzLjggMy40IDcgNy41IDdINDg0djg0LjljMCAzLjkgMy4yIDcuMSA3IDcuMWg0MmMzLjkgMCA3LTMuMiA3LTcuMVY1ODRoODQuNWM0LjEgMCA3LjUtMy4yIDcuNS03di00MmMwLTMuOS0zLjQtNy03LjUtN0g1NDB2LTg0LjljMC0zLjktMy4xLTcuMS03LTcuMWgtNDJjLTMuOCAwLTcgMy4yLTcgNy4xem0zOTYtMTQ0LjdINTIxTDQwMy43IDE4Ni4yYTguMTUgOC4xNSAwIDAwLTUuNS0yLjJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY1OTJjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjMzMC40YzAtMTcuNy0xNC4zLTMyLTMyLTMyek04NDAgNzY4SDE4NFYyNTZoMTg4LjVsMTE5LjYgMTE0LjRIODQwVjc2OHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(FolderAddOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FolderAddOutlined';
}
export default RefIcon;