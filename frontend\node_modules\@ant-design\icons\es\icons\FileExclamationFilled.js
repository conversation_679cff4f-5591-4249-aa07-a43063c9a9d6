import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FileExclamationFilledSvg from "@ant-design/icons-svg/es/asn/FileExclamationFilled";
import AntdIcon from "../components/AntdIcon";
var FileExclamationFilled = function FileExclamationFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: FileExclamationFilledSvg
  }));
};

/**![file-exclamation](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NC42IDI4OC43YzYgNiA5LjQgMTQuMSA5LjQgMjIuNlY5MjhjMCAxNy43LTE0LjMgMzItMzIgMzJIMTkyYy0xNy43IDAtMzItMTQuMy0zMi0zMlY5NmMwLTE3LjcgMTQuMy0zMiAzMi0zMmg0MjQuN2M4LjUgMCAxNi43IDMuNCAyMi43IDkuNGwyMTUuMiAyMTUuM3pNNzkwLjIgMzI2TDYwMiAxMzcuOFYzMjZoMTg4LjJ6TTUxMiA3ODRhNDAgNDAgMCAxMDAtODAgNDAgNDAgMCAwMDAgODB6bTMyLTE1MlY0NDhhOCA4IDAgMDAtOC04aC00OGE4IDggMCAwMC04IDh2MTg0YTggOCAwIDAwOCA4aDQ4YTggOCAwIDAwOC04eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(FileExclamationFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FileExclamationFilled';
}
export default RefIcon;