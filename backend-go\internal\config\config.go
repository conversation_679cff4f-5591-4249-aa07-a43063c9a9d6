package config

import (
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

// Config 应用配置结构
type Config struct {
	// 应用基本配置
	Port        string
	Environment string
	AppName     string

	// 数据库配置
	DatabaseURL string

	// Redis配置
	RedisURL string

	// LLM配置
	OpenAIAPIKey  string
	OpenAIModel   string
	OpenAIBaseURL string

	// 安全配置
	JWTSecret string

	// 其他配置
	CORSOrigins            string
	HotnessUpdateInterval  int
	DataCollectionInterval int
}

// Load 加载配置
func Load() *Config {
	// 尝试加载.env文件
	_ = godotenv.Load()

	return &Config{
		// 应用基本配置
		Port:        getEnv("PORT", "8000"),
		Environment: getEnv("APP_ENV", "development"),
		AppName:     getEnv("APP_NAME", "AI炒股网站"),

		// 数据库配置
		DatabaseURL: getEnv("DATABASE_URL", "../data/ai_cg.db"),

		// Redis配置
		RedisURL: getEnv("REDIS_URL", ""),

		// LLM配置
		OpenAIAPIKey:  getEnv("OPENAI_API_KEY", ""),
		OpenAIModel:   getEnv("OPENAI_MODEL", "gpt-3.5-turbo"),
		OpenAIBaseURL: getEnv("OPENAI_BASE_URL", "https://api.openai.com/v1"),

		// 安全配置
		JWTSecret: getEnv("JWT_SECRET", "your-secret-key-change-in-production"),

		// 其他配置
		CORSOrigins:            getEnv("CORS_ORIGINS", "http://localhost:3000,http://localhost:3001"),
		HotnessUpdateInterval:  getEnvAsInt("HOTNESS_UPDATE_INTERVAL", 30),
		DataCollectionInterval: getEnvAsInt("DATA_COLLECTION_INTERVAL", 60),
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt 获取环境变量并转换为整数
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}
