"""
LLM API路由
提供AI问答和分析功能
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Optional

from app.models.schemas import LLMRequest, LLMResponse, MessageResponse
from app.services.llm_service import llm_service
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.post("/ask", response_model=LLMResponse)
async def ask_llm(request: LLMRequest, user_id: Optional[int] = None):
    """
    向AI提问
    
    - **question**: 用户问题
    - **context_type**: 上下文类型 (sector, stock, market, general)
    - **context_id**: 上下文ID (可选，板块ID或股票代码)
    - **model_name**: 模型名称 (可选)
    - **max_tokens**: 最大token数 (可选)
    - **temperature**: 温度参数 (可选)
    """
    try:
        # 验证输入
        if not request.question.strip():
            raise HTTPException(status_code=400, detail="问题不能为空")
        
        if len(request.question) > 1000:
            raise HTTPException(status_code=400, detail="问题长度不能超过1000字符")
        
        # 调用LLM服务
        result = await llm_service.ask_question(
            question=request.question,
            context_type=request.context_type,
            context_id=request.context_id,
            user_id=user_id,
            model_name=request.model_name,
            max_tokens=request.max_tokens,
            temperature=request.temperature
        )
        
        # 检查是否有错误
        if result.get("error"):
            logger.warning(f"LLM请求出错: {result['error']}")
        
        return LLMResponse(
            answer=result["answer"],
            references=result.get("references", []),
            confidence=result.get("confidence"),
            token_usage=result.get("token_usage"),
            processing_time_ms=result.get("processing_time_ms"),
            model_name=result.get("model_name", "unknown")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"LLM API错误: {e}")
        raise HTTPException(status_code=500, detail="AI服务暂时不可用，请稍后重试")


@router.get("/stats")
async def get_llm_stats():
    """
    获取LLM使用统计
    """
    try:
        stats = await llm_service.get_usage_stats()
        return stats
        
    except Exception as e:
        logger.error(f"获取LLM统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取统计信息失败")


@router.get("/health")
async def llm_health_check():
    """
    LLM服务健康检查
    """
    return {
        "status": "healthy" if llm_service.is_available else "unavailable",
        "is_available": llm_service.is_available,
        "service": "LLM"
    }


@router.post("/explain/sector/{sector_id}")
async def explain_sector(sector_id: str, user_id: Optional[int] = None):
    """
    解释板块走势
    
    - **sector_id**: 板块ID
    """
    try:
        question = f"请分析{sector_id}板块的当前走势和驱动因素"
        
        result = await llm_service.ask_question(
            question=question,
            context_type="sector",
            context_id=sector_id,
            user_id=user_id
        )
        
        return LLMResponse(
            answer=result["answer"],
            references=result.get("references", []),
            confidence=result.get("confidence"),
            token_usage=result.get("token_usage"),
            processing_time_ms=result.get("processing_time_ms"),
            model_name=result.get("model_name", "unknown")
        )
        
    except Exception as e:
        logger.error(f"板块解释失败: {e}")
        raise HTTPException(status_code=500, detail="板块分析失败")


@router.post("/explain/stock/{symbol}")
async def explain_stock(symbol: str, user_id: Optional[int] = None):
    """
    解释个股走势
    
    - **symbol**: 股票代码
    """
    try:
        question = f"请分析{symbol}股票的当前走势和基本面情况"
        
        result = await llm_service.ask_question(
            question=question,
            context_type="stock",
            context_id=symbol,
            user_id=user_id
        )
        
        return LLMResponse(
            answer=result["answer"],
            references=result.get("references", []),
            confidence=result.get("confidence"),
            token_usage=result.get("token_usage"),
            processing_time_ms=result.get("processing_time_ms"),
            model_name=result.get("model_name", "unknown")
        )
        
    except Exception as e:
        logger.error(f"个股解释失败: {e}")
        raise HTTPException(status_code=500, detail="个股分析失败")


@router.post("/market/analysis")
async def market_analysis(user_id: Optional[int] = None):
    """
    市场整体分析
    """
    try:
        question = "请分析当前A股市场的整体情况，包括热点板块、市场情绪和投资机会"
        
        result = await llm_service.ask_question(
            question=question,
            context_type="market",
            context_id="overview",
            user_id=user_id
        )
        
        return LLMResponse(
            answer=result["answer"],
            references=result.get("references", []),
            confidence=result.get("confidence"),
            token_usage=result.get("token_usage"),
            processing_time_ms=result.get("processing_time_ms"),
            model_name=result.get("model_name", "unknown")
        )
        
    except Exception as e:
        logger.error(f"市场分析失败: {e}")
        raise HTTPException(status_code=500, detail="市场分析失败")

