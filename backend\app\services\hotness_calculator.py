"""
热点板块计算服务
实现板块热度的多因子计算模型
"""

import asyncio
import json
import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging

from app.core.database import db_manager
from app.core.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


class HotnessCalculatorService:
    """热度计算服务"""
    
    def __init__(self):
        self.is_running = False
        self.calculation_task = None
        self.factors_weight = settings.HOTNESS_FACTORS_WEIGHT
        
    async def start_calculation(self):
        """启动热度计算任务"""
        if self.is_running:
            logger.warning("热度计算服务已经在运行")
            return
            
        self.is_running = True
        self.calculation_task = asyncio.create_task(self._calculation_loop())
        logger.info("🔥 热度计算服务已启动")
        
    async def stop(self):
        """停止热度计算任务"""
        self.is_running = False
        if self.calculation_task:
            self.calculation_task.cancel()
            try:
                await self.calculation_task
            except asyncio.CancelledError:
                pass
        logger.info("🛑 热度计算服务已停止")
        
    async def _calculation_loop(self):
        """计算循环"""
        while self.is_running:
            try:
                await self.calculate_all_sectors_hotness()
                await asyncio.sleep(settings.HOTNESS_UPDATE_INTERVAL)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"热度计算循环错误: {e}")
                await asyncio.sleep(30)  # 错误时等待30秒再重试
                
    async def calculate_all_sectors_hotness(self):
        """计算所有板块的热度"""
        try:
            # 获取所有活跃板块
            sectors_sql = "SELECT id FROM sectors WHERE is_active = 1"
            sectors = await db_manager.execute_raw_sql(sectors_sql)
            
            calculation_time = datetime.now()
            
            for sector in sectors:
                sector_id = sector["id"]
                try:
                    hotness_data = await self.calculate_sector_hotness(sector_id)
                    if hotness_data:
                        await self._save_hotness_data(sector_id, hotness_data, calculation_time)
                except Exception as e:
                    logger.error(f"计算板块 {sector_id} 热度失败: {e}")
                    continue
                    
            logger.info(f"完成 {len(sectors)} 个板块的热度计算")
            
        except Exception as e:
            logger.error(f"计算所有板块热度失败: {e}")
            
    async def calculate_sector_hotness(self, sector_id: str) -> Optional[Dict]:
        """计算单个板块的热度"""
        try:
            # 获取板块成员
            members = await self._get_sector_members(sector_id)
            if not members:
                logger.warning(f"板块 {sector_id} 没有成员股票")
                return None
                
            # 计算各个因子
            factors = {}
            
            # 1. 成交额因子
            factors["turnover"] = await self._calculate_turnover_factor(members)
            
            # 2. 量比因子  
            factors["volume_ratio"] = await self._calculate_volume_ratio_factor(members)
            
            # 3. 上涨股票比例因子
            factors["pct_up"] = await self._calculate_up_ratio_factor(members)
            
            # 4. 新闻热度因子
            factors["news_count"] = await self._calculate_news_factor(sector_id, members)
            
            # 5. 资金流向因子
            factors["fund_flow"] = await self._calculate_fund_flow_factor(members)
            
            # 计算加权热度分数
            hotness = self._calculate_weighted_hotness(factors)
            
            # 获取其他统计信息
            stats = await self._calculate_sector_stats(members)
            
            # 获取龙头股票
            top_stocks = await self._get_top_stocks(members)
            
            result = {
                "hotness": hotness,
                "today_change": stats.get("avg_change", 0.0),
                "turnover": stats.get("total_turnover", 0),
                "volume_ratio": stats.get("avg_volume_ratio", 0.0),
                "pct_up": stats.get("pct_up", 0.0),
                "pct_down": stats.get("pct_down", 0.0),
                "top_stocks": top_stocks,
                "factors": factors
            }
            
            return result
            
        except Exception as e:
            logger.error(f"计算板块 {sector_id} 热度失败: {e}")
            return None
            
    async def _get_sector_members(self, sector_id: str) -> List[Dict]:
        """获取板块成员"""
        sql = """
        SELECT sm.symbol, sm.weight, sm.is_leader, s.name
        FROM sector_members sm
        JOIN stocks s ON sm.symbol = s.symbol
        WHERE sm.sector_id = :sector_id AND s.is_active = 1
        """
        return await db_manager.execute_raw_sql(sql, {"sector_id": sector_id})
        
    async def _calculate_turnover_factor(self, members: List[Dict]) -> float:
        """计算成交额因子"""
        try:
            if not members:
                return 0.0
                
            symbols = [member["symbol"] for member in members]
            
            # 获取今日和历史5日平均成交额
            sql = """
            SELECT 
                symbol,
                SUM(CASE WHEN DATE(ts) = DATE('now') THEN amount ELSE 0 END) as today_amount,
                AVG(CASE WHEN DATE(ts) >= DATE('now', '-5 days') AND DATE(ts) < DATE('now') 
                         THEN amount ELSE NULL END) as avg_amount_5d
            FROM stock_kline 
            WHERE symbol IN ({}) 
            AND period = '1d'
            AND ts >= DATE('now', '-6 days')
            GROUP BY symbol
            """.format(','.join(['?' for _ in symbols]))
            
            results = await db_manager.execute_raw_sql(sql, symbols)
            
            total_today = sum(row["today_amount"] or 0 for row in results)
            total_avg_5d = sum(row["avg_amount_5d"] or 0 for row in results)
            
            if total_avg_5d > 0:
                ratio = total_today / total_avg_5d
                # 使用sigmoid函数标准化到0-1
                return self._sigmoid(ratio - 1.0, scale=2.0)
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"计算成交额因子失败: {e}")
            return 0.0
            
    async def _calculate_volume_ratio_factor(self, members: List[Dict]) -> float:
        """计算量比因子"""
        try:
            if not members:
                return 0.0
                
            symbols = [member["symbol"] for member in members]
            
            # 获取量比数据（简化计算，实际应该更复杂）
            sql = """
            SELECT symbol, volume
            FROM stock_kline 
            WHERE symbol IN ({})
            AND period = '1d'
            AND DATE(ts) = DATE('now')
            """.format(','.join(['?' for _ in symbols]))
            
            today_volumes = await db_manager.execute_raw_sql(sql, symbols)
            
            # 获取历史平均量
            sql = """
            SELECT symbol, AVG(volume) as avg_volume
            FROM stock_kline 
            WHERE symbol IN ({})
            AND period = '1d'
            AND DATE(ts) >= DATE('now', '-30 days')
            AND DATE(ts) < DATE('now')
            GROUP BY symbol
            """.format(','.join(['?' for _ in symbols]))
            
            avg_volumes = await db_manager.execute_raw_sql(sql, symbols)
            avg_volume_map = {row["symbol"]: row["avg_volume"] for row in avg_volumes}
            
            # 计算加权平均量比
            total_weight = sum(member["weight"] for member in members)
            weighted_ratio = 0.0
            
            for today_vol in today_volumes:
                symbol = today_vol["symbol"]
                today_volume = today_vol["volume"] or 0
                avg_volume = avg_volume_map.get(symbol, 0)
                
                if avg_volume > 0:
                    volume_ratio = today_volume / avg_volume
                    weight = next((m["weight"] for m in members if m["symbol"] == symbol), 0)
                    weighted_ratio += volume_ratio * weight / total_weight
            
            return self._sigmoid(weighted_ratio - 1.0, scale=1.5)
            
        except Exception as e:
            logger.error(f"计算量比因子失败: {e}")
            return 0.0
            
    async def _calculate_up_ratio_factor(self, members: List[Dict]) -> float:
        """计算上涨股票比例因子"""
        try:
            if not members:
                return 0.0
                
            symbols = [member["symbol"] for member in members]
            
            sql = """
            SELECT 
                symbol,
                (close - open) / open * 100 as change_pct
            FROM stock_kline
            WHERE symbol IN ({})
            AND period = '1d'
            AND DATE(ts) = DATE('now')
            """.format(','.join(['?' for _ in symbols]))
            
            results = await db_manager.execute_raw_sql(sql, symbols)
            
            if not results:
                return 0.0
                
            up_count = sum(1 for row in results if (row["change_pct"] or 0) > 0)
            total_count = len(results)
            
            up_ratio = up_count / total_count if total_count > 0 else 0
            
            # 上涨比例超过50%为正面信号
            return max(0, (up_ratio - 0.5) * 2)
            
        except Exception as e:
            logger.error(f"计算上涨比例因子失败: {e}")
            return 0.0
            
    async def _calculate_news_factor(self, sector_id: str, members: List[Dict]) -> float:
        """计算新闻热度因子"""
        try:
            symbols = [member["symbol"] for member in members]
            
            # 获取最近24小时的新闻数量
            sql = """
            SELECT COUNT(*) as news_count
            FROM news
            WHERE (
                symbols LIKE '%{}%' 
                {}
            )
            AND published_at >= datetime('now', '-1 day')
            """.format(
                sector_id,
                " OR " + " OR ".join([f"symbols LIKE '%{symbol}%'" for symbol in symbols[:10]])  # 限制查询复杂度
            )
            
            result = await db_manager.execute_raw_sql_single(sql)
            news_count = result["news_count"] if result else 0
            
            # 标准化新闻数量 (假设10条新闻为满分)
            return min(1.0, news_count / 10.0)
            
        except Exception as e:
            logger.error(f"计算新闻因子失败: {e}")
            return 0.0
            
    async def _calculate_fund_flow_factor(self, members: List[Dict]) -> float:
        """计算资金流向因子"""
        try:
            symbols = [member["symbol"] for member in members]
            
            sql = """
            SELECT SUM(net_inflow) as total_net_inflow
            FROM fund_flow
            WHERE symbol IN ({})
            AND DATE(ts) = DATE('now')
            """.format(','.join(['?' for _ in symbols]))
            
            result = await db_manager.execute_raw_sql_single(sql, symbols)
            net_inflow = result["total_net_inflow"] if result and result["total_net_inflow"] else 0
            
            # 标准化资金流向 (使用tanh函数)
            normalized = math.tanh(net_inflow / 1000000000)  # 10亿为标准
            return (normalized + 1) / 2  # 转换到0-1区间
            
        except Exception as e:
            logger.error(f"计算资金流向因子失败: {e}")
            return 0.0
            
    def _calculate_weighted_hotness(self, factors: Dict[str, float]) -> float:
        """计算加权热度分数"""
        total_weight = sum(self.factors_weight.values())
        weighted_sum = 0.0
        
        for factor_name, factor_value in factors.items():
            weight = self.factors_weight.get(factor_name, 0)
            weighted_sum += factor_value * weight
            
        # 标准化到0-100区间
        normalized_score = weighted_sum / total_weight if total_weight > 0 else 0
        return min(100.0, max(0.0, normalized_score * 100))
        
    def _sigmoid(self, x: float, scale: float = 1.0) -> float:
        """Sigmoid标准化函数"""
        try:
            return 1 / (1 + math.exp(-x * scale))
        except OverflowError:
            return 0.0 if x < 0 else 1.0
            
    async def _calculate_sector_stats(self, members: List[Dict]) -> Dict:
        """计算板块统计信息"""
        try:
            symbols = [member["symbol"] for member in members]
            
            sql = """
            SELECT 
                symbol,
                open, close, volume, amount,
                (close - open) / open * 100 as change_pct
            FROM stock_kline
            WHERE symbol IN ({})
            AND period = '1d'
            AND DATE(ts) = DATE('now')
            """.format(','.join(['?' for _ in symbols]))
            
            results = await db_manager.execute_raw_sql(sql, symbols)
            
            if not results:
                return {}
                
            changes = [row["change_pct"] or 0 for row in results]
            volumes = [row["volume"] or 0 for row in results]
            amounts = [row["amount"] or 0 for row in results]
            
            up_count = sum(1 for change in changes if change > 0)
            down_count = sum(1 for change in changes if change < 0)
            total_count = len(changes)
            
            return {
                "avg_change": sum(changes) / len(changes) if changes else 0,
                "total_turnover": sum(amounts),
                "avg_volume_ratio": 1.0,  # 简化处理
                "pct_up": up_count / total_count if total_count > 0 else 0,
                "pct_down": down_count / total_count if total_count > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"计算板块统计失败: {e}")
            return {}
            
    async def _get_top_stocks(self, members: List[Dict], limit: int = 3) -> List[str]:
        """获取板块龙头股票"""
        try:
            # 优先返回标记为龙头的股票
            leaders = [member["symbol"] for member in members if member.get("is_leader")]
            if leaders:
                return leaders[:limit]
                
            # 按权重排序
            sorted_members = sorted(members, key=lambda x: x.get("weight", 0), reverse=True)
            return [member["symbol"] for member in sorted_members[:limit]]
            
        except Exception as e:
            logger.error(f"获取龙头股票失败: {e}")
            return []
            
    async def _save_hotness_data(self, sector_id: str, hotness_data: Dict, timestamp: datetime):
        """保存热度数据"""
        try:
            sql = """
            INSERT OR REPLACE INTO sector_hotness 
            (sector_id, ts, hotness, today_change, turnover, volume_ratio, 
             pct_up, pct_down, top_stocks, factors, source_version)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = [
                sector_id,
                timestamp.isoformat(),
                hotness_data["hotness"],
                hotness_data["today_change"],
                hotness_data["turnover"],
                hotness_data["volume_ratio"],
                hotness_data["pct_up"],
                hotness_data["pct_down"],
                json.dumps(hotness_data["top_stocks"]),
                json.dumps(hotness_data["factors"]),
                "v1.0"
            ]
            
            await db_manager.execute_insert(sql, params)
            
        except Exception as e:
            logger.error(f"保存热度数据失败: {e}")
            raise

