"""
应用配置管理
"""

from pydantic_settings import BaseSettings
from typing import Optional, List
import os


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基本信息
    APP_NAME: str = "AI炒股网站"
    APP_VERSION: str = "1.0.0"
    APP_ENV: str = "development"
    DEBUG: bool = True
    
    # 服务配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///data/ai_cg.db"
    DATABASE_ECHO: bool = False
    
    # Redis配置（可选）
    REDIS_URL: Optional[str] = None
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: Optional[str] = None
    REDIS_DB: int = 0
    
    # JWT配置
    JWT_SECRET_KEY: str = "your-secret-key-change-in-production"
    JWT_ALGORITHM: str = "HS256"
    JWT_EXPIRE_HOURS: int = 24
    
    # CORS配置
    CORS_ORIGINS: str = "http://localhost:3000,http://localhost:3001"
    
    # LLM配置
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_MODEL: str = "gpt-3.5-turbo"
    OPENAI_BASE_URL: str = "https://api.openai.com/v1"
    OPENAI_MAX_TOKENS: int = 2000
    OPENAI_TEMPERATURE: float = 0.7
    
    # 数据源配置
    TUSHARE_TOKEN: Optional[str] = None
    EASTMONEY_API_KEY: Optional[str] = None
    POLYGON_API_KEY: Optional[str] = None
    ALPHA_VANTAGE_API_KEY: Optional[str] = None
    NEWS_API_KEY: Optional[str] = None
    
    # 缓存配置
    CACHE_TTL_SECONDS: int = 300
    HOT_DATA_CACHE_TTL: int = 60
    
    # 数据更新配置
    HOTNESS_UPDATE_INTERVAL: int = 30  # 秒
    DATA_COLLECTION_INTERVAL: int = 60  # 秒
    KLINE_UPDATE_INTERVAL: int = 300  # 秒
    NEWS_UPDATE_INTERVAL: int = 600  # 秒
    
    # 热度计算配置
    HOTNESS_FACTORS_WEIGHT: dict = {
        "turnover": 0.3,
        "volume_ratio": 0.2, 
        "pct_up": 0.25,
        "news_count": 0.15,
        "fund_flow": 0.1
    }
    
    # API限制配置
    RATE_LIMIT_PER_MINUTE: int = 100
    MAX_HOT_SECTORS_DISPLAY: int = 10
    MAX_SECTOR_MEMBERS: int = 100
    
    # WebSocket配置
    WS_HEARTBEAT_INTERVAL: int = 30
    WS_MAX_CONNECTIONS: int = 1000
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: Optional[str] = None
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    BCRYPT_ROUNDS: int = 12
    
    # 邮件配置
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    FROM_EMAIL: Optional[str] = None
    
    # 监控配置
    SENTRY_DSN: Optional[str] = None
    PROMETHEUS_ENABLED: bool = True
    
    # 文件存储配置
    UPLOAD_DIR: str = "data/uploads"
    BACKUP_DIR: str = "data/backups"
    CACHE_DIR: str = "data/cache"
    
    # 数据保留配置
    DATA_RETENTION_DAYS: int = 365
    LOG_RETENTION_DAYS: int = 30
    TICK_DATA_RETENTION_DAYS: int = 7
    
    @property
    def redis_url(self) -> Optional[str]:
        """构建Redis URL"""
        if self.REDIS_URL:
            return self.REDIS_URL
        
        if not self.REDIS_HOST:
            return None
            
        auth = f":{self.REDIS_PASSWORD}@" if self.REDIS_PASSWORD else ""
        return f"redis://{auth}{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.APP_ENV.lower() == "production"
    
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.APP_ENV.lower() == "development"
    
    def get_data_source_config(self, source: str) -> Optional[str]:
        """获取数据源配置"""
        source_map = {
            "tushare": self.TUSHARE_TOKEN,
            "eastmoney": self.EASTMONEY_API_KEY,
            "polygon": self.POLYGON_API_KEY,
            "alpha_vantage": self.ALPHA_VANTAGE_API_KEY,
            "news_api": self.NEWS_API_KEY,
        }
        return source_map.get(source.lower())
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings


# 环境变量验证
def validate_settings():
    """验证必要的配置项"""
    required_for_production = [
        "JWT_SECRET_KEY",
        "SECRET_KEY", 
        "OPENAI_API_KEY"
    ]
    
    if settings.is_production:
        missing_configs = []
        for config in required_for_production:
            if not getattr(settings, config) or getattr(settings, config) == "your-secret-key-change-in-production":
                missing_configs.append(config)
        
        if missing_configs:
            raise ValueError(f"生产环境缺少必要配置: {', '.join(missing_configs)}")
    
    return True


# 在导入时验证配置
if __name__ != "__main__":
    validate_settings()

