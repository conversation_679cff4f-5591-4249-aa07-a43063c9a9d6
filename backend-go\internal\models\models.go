package models

import (
	"encoding/json"
)

// HotSector 热点板块结构
type HotSector struct {
	SectorID    string          `json:"sector_id" db:"sector_id"`
	Name        string          `json:"name" db:"name"`
	NameEn      *string         `json:"name_en" db:"name_en"`
	Hotness     float64         `json:"hotness" db:"hotness"`
	TodayChange *float64        `json:"today_change" db:"today_change"`
	Turnover    *int64          `json:"turnover" db:"turnover"`
	VolumeRatio *float64        `json:"volume_ratio" db:"volume_ratio"`
	PctUp       *float64        `json:"pct_up" db:"pct_up"`
	PctDown     *float64        `json:"pct_down" db:"pct_down"`
	TopStocks   json.RawMessage `json:"top_stocks" db:"top_stocks"`
	Factors     json.RawMessage `json:"factors" db:"factors"`
	UpdatedAt   string          `json:"updated_at" db:"updated_at"`
}

// SectorDetail 板块详情结构
type SectorDetail struct {
	SectorID    string          `json:"sector_id" db:"id"`
	Name        string          `json:"name" db:"name"`
	NameEn      *string         `json:"name_en" db:"name_en"`
	Description *string         `json:"description" db:"description"`
	ParentID    *string         `json:"parent_id" db:"parent_id"`
	Hotness     float64         `json:"hotness"`
	TodayChange *float64        `json:"today_change"`
	Turnover    *int64          `json:"turnover"`
	VolumeRatio *float64        `json:"volume_ratio"`
	PctUp       *float64        `json:"pct_up"`
	PctDown     *float64        `json:"pct_down"`
	Factors     json.RawMessage `json:"factors"`
	Members     []SectorMember  `json:"members"`
	UpdatedAt   *string         `json:"updated_at"`
}

// SectorMember 板块成员结构
type SectorMember struct {
	Symbol       string   `json:"symbol" db:"symbol"`
	Name         string   `json:"name" db:"name"`
	Weight       float64  `json:"weight" db:"weight"`
	IsLeader     bool     `json:"is_leader" db:"is_leader"`
	CurrentPrice *float64 `json:"current_price" db:"close"`
	OpenPrice    *float64 `json:"open_price" db:"open"`
	HighPrice    *float64 `json:"high_price" db:"high"`
	LowPrice     *float64 `json:"low_price" db:"low"`
	Volume       *int64   `json:"volume" db:"volume"`
	Amount       *float64 `json:"amount" db:"amount"`
	ChangePct    *float64 `json:"change_pct" db:"change_pct"`
	LastUpdate   *string  `json:"last_update" db:"last_update"`
}

// Stock 股票结构
type Stock struct {
	Symbol    string  `json:"symbol" db:"symbol"`
	Name      string  `json:"name" db:"name"`
	SectorID  *string `json:"sector_id" db:"sector_id"`
	Market    string  `json:"market" db:"market"`
	ListDate  *string `json:"list_date" db:"list_date"`
	MarketCap *int64  `json:"market_cap" db:"market_cap"`
	IsActive  bool    `json:"is_active" db:"is_active"`
	CreatedAt string  `json:"created_at" db:"created_at"`
	UpdatedAt string  `json:"updated_at" db:"updated_at"`
}

// StockKline K线数据结构
type StockKline struct {
	ID        int     `json:"id" db:"id"`
	Symbol    string  `json:"symbol" db:"symbol"`
	Timestamp string  `json:"ts" db:"ts"`
	Open      float64 `json:"open" db:"open"`
	High      float64 `json:"high" db:"high"`
	Low       float64 `json:"low" db:"low"`
	Close     float64 `json:"close" db:"close"`
	Volume    int64   `json:"volume" db:"volume"`
	Amount    float64 `json:"amount" db:"amount"`
	Period    string  `json:"period" db:"period"`
	CreatedAt string  `json:"created_at" db:"created_at"`
}

// News 新闻结构
type News struct {
	ID             string          `json:"id" db:"id"`
	Title          string          `json:"title" db:"title"`
	Content        *string         `json:"content" db:"content"`
	PublishedAt    string          `json:"published_at" db:"published_at"`
	Source         *string         `json:"source" db:"source"`
	URL            *string         `json:"url" db:"url"`
	Symbols        json.RawMessage `json:"symbols" db:"symbols"`
	SentimentScore *float64        `json:"sentiment_score" db:"sentiment_score"`
	Keywords       json.RawMessage `json:"keywords" db:"keywords"`
	CreatedAt      string          `json:"created_at" db:"created_at"`
}

// User 用户结构
type User struct {
	ID           int     `json:"id" db:"id"`
	Email        string  `json:"email" db:"email"`
	PasswordHash string  `json:"-" db:"password_hash"`
	Username     *string `json:"username" db:"username"`
	AvatarURL    *string `json:"avatar_url" db:"avatar_url"`
	IsActive     bool    `json:"is_active" db:"is_active"`
	IsPremium    bool    `json:"is_premium" db:"is_premium"`
	CreatedAt    string  `json:"created_at" db:"created_at"`
	UpdatedAt    string  `json:"updated_at" db:"updated_at"`
}

// SystemStats 系统统计结构
type SystemStats struct {
	TotalStocks             int     `json:"total_stocks"`
	TotalSectors            int     `json:"total_sectors"`
	TotalUsers              int     `json:"total_users"`
	ActiveConnections       int     `json:"active_connections"`
	DataCollectorStatus     bool    `json:"data_collector_status"`
	HotnessCalculatorStatus bool    `json:"hotness_calculator_status"`
	DatabaseSizeMB          float64 `json:"database_size_mb"`
	LastUpdate              *string `json:"last_update"`
}

// LLMRequest LLM请求结构
type LLMRequest struct {
	Question    string   `json:"question" binding:"required"`
	ContextType string   `json:"context_type" binding:"required"`
	ContextID   *string  `json:"context_id"`
	ModelName   *string  `json:"model_name"`
	MaxTokens   *int     `json:"max_tokens"`
	Temperature *float64 `json:"temperature"`
}

// LLMResponse LLM响应结构
type LLMResponse struct {
	Answer           string                   `json:"answer"`
	References       []map[string]interface{} `json:"references"`
	Confidence       *float64                 `json:"confidence"`
	TokenUsage       *int                     `json:"token_usage"`
	ProcessingTimeMs *int                     `json:"processing_time_ms"`
	ModelName        string                   `json:"model_name"`
}

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message,omitempty"`
	Code    int    `json:"code,omitempty"`
}

// SuccessResponse 成功响应结构
type SuccessResponse struct {
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// PaginationResponse 分页响应结构
type PaginationResponse struct {
	Items      interface{} `json:"items"`
	Total      int         `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
}

// HealthResponse 健康检查响应结构
type HealthResponse struct {
	Status    string                 `json:"status"`
	Service   string                 `json:"service"`
	Version   string                 `json:"version"`
	Timestamp int64                  `json:"timestamp"`
	Details   map[string]interface{} `json:"details,omitempty"`
}
