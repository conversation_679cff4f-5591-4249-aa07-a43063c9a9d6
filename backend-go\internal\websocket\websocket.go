package websocket

import (
	"encoding/json"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		// 允许所有来源（生产环境应该做严格检查）
		return true
	},
}

// Manager WebSocket连接管理器
type Manager struct {
	clients     map[*websocket.Conn]bool
	broadcast   chan []byte
	register    chan *websocket.Conn
	unregister  chan *websocket.Conn
	mu          sync.RWMutex
	subscribers map[*websocket.Conn]map[string]bool // 客户端订阅的频道
}

// Message WebSocket消息结构
type Message struct {
	Type      string                 `json:"type"`
	Data      map[string]interface{} `json:"data"`
	Timestamp string                 `json:"timestamp"`
}

// SubscribeMessage 订阅消息结构
type SubscribeMessage struct {
	Type     string   `json:"type"`
	Channels []string `json:"channels"`
}

// NewManager 创建新的WebSocket管理器
func NewManager() *Manager {
	manager := &Manager{
		clients:     make(map[*websocket.Conn]bool),
		broadcast:   make(chan []byte),
		register:    make(chan *websocket.Conn),
		unregister:  make(chan *websocket.Conn),
		subscribers: make(map[*websocket.Conn]map[string]bool),
	}

	go manager.run()
	return manager
}

// run 运行WebSocket管理器
func (m *Manager) run() {
	for {
		select {
		case conn := <-m.register:
			m.mu.Lock()
			m.clients[conn] = true
			m.subscribers[conn] = make(map[string]bool)
			m.mu.Unlock()
			log.Printf("WebSocket客户端连接: %d个活跃连接", len(m.clients))

		case conn := <-m.unregister:
			m.mu.Lock()
			if _, ok := m.clients[conn]; ok {
				delete(m.clients, conn)
				delete(m.subscribers, conn)
				conn.Close()
			}
			m.mu.Unlock()
			log.Printf("WebSocket客户端断开: %d个活跃连接", len(m.clients))

		case message := <-m.broadcast:
			m.mu.RLock()
			for conn := range m.clients {
				select {
				case <-make(chan struct{}):
					// 非阻塞发送
					go func(c *websocket.Conn) {
						if err := c.WriteMessage(websocket.TextMessage, message); err != nil {
							log.Printf("WebSocket发送消息失败: %v", err)
							m.unregister <- c
						}
					}(conn)
				default:
					close(make(chan struct{}))
					m.unregister <- conn
				}
			}
			m.mu.RUnlock()
		}
	}
}

// HandleWebSocket 处理WebSocket连接
func (m *Manager) HandleWebSocket(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}

	m.register <- conn

	// 发送欢迎消息
	welcomeMsg := Message{
		Type: "connected",
		Data: map[string]interface{}{
			"message": "WebSocket连接成功",
			"status":  "connected",
		},
		Timestamp: getCurrentTimestamp(),
	}

	if data, err := json.Marshal(welcomeMsg); err == nil {
		conn.WriteMessage(websocket.TextMessage, data)
	}

	go m.handleMessages(conn)
}

// handleMessages 处理客户端消息
func (m *Manager) handleMessages(conn *websocket.Conn) {
	defer func() {
		m.unregister <- conn
	}()

	for {
		_, messageBytes, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket错误: %v", err)
			}
			break
		}

		var subMsg SubscribeMessage
		if err := json.Unmarshal(messageBytes, &subMsg); err != nil {
			log.Printf("解析WebSocket消息失败: %v", err)
			continue
		}

		m.handleSubscription(conn, &subMsg)
	}
}

// handleSubscription 处理订阅请求
func (m *Manager) handleSubscription(conn *websocket.Conn, msg *SubscribeMessage) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if subscribers, ok := m.subscribers[conn]; ok {
		switch msg.Type {
		case "subscribe":
			for _, channel := range msg.Channels {
				subscribers[channel] = true
			}
			m.sendResponse(conn, "subscribed", map[string]interface{}{
				"channels": msg.Channels,
			})

		case "unsubscribe":
			for _, channel := range msg.Channels {
				delete(subscribers, channel)
			}
			m.sendResponse(conn, "unsubscribed", map[string]interface{}{
				"channels": msg.Channels,
			})
		}
	}
}

// sendResponse 发送响应消息
func (m *Manager) sendResponse(conn *websocket.Conn, msgType string, data map[string]interface{}) {
	response := Message{
		Type:      msgType,
		Data:      data,
		Timestamp: getCurrentTimestamp(),
	}

	if responseBytes, err := json.Marshal(response); err == nil {
		conn.WriteMessage(websocket.TextMessage, responseBytes)
	}
}

// Broadcast 广播消息
func (m *Manager) Broadcast(message Message) {
	if data, err := json.Marshal(message); err == nil {
		m.broadcast <- data
	}
}

// BroadcastToChannel 向特定频道广播消息
func (m *Manager) BroadcastToChannel(channel string, message Message) {
	if data, err := json.Marshal(message); err == nil {
		m.mu.RLock()
		defer m.mu.RUnlock()

		for conn, subscribers := range m.subscribers {
			if subscribers[channel] {
				go func(c *websocket.Conn) {
					if err := c.WriteMessage(websocket.TextMessage, data); err != nil {
						log.Printf("发送频道消息失败: %v", err)
						m.unregister <- c
					}
				}(conn)
			}
		}
	}
}

// GetActiveConnections 获取活跃连接数
func (m *Manager) GetActiveConnections() int {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return len(m.clients)
}

// 辅助函数
func getCurrentTimestamp() string {
	return time.Now().Format(time.RFC3339)
}
