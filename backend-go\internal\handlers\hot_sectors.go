package handlers

import (
	"ai-cg-backend/internal/models"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// GetHotSectors 获取热点板块列表
// @Summary 获取热点板块列表
// @Description 获取当前市场热点板块排行榜
// @Tags 热点板块
// @Accept json
// @Produce json
// @Param market query string false "市场代码" default(cn)
// @Param limit query int false "返回数量限制" default(10)
// @Param min_hotness query number false "最小热度阈值" default(0)
// @Param sort_by query string false "排序字段" default(hotness)
// @Success 200 {array} models.HotSector
// @Failure 500 {object} models.ErrorResponse
// @Router /hot-sectors [get]
func GetHotSectors(db *sql.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 解析查询参数
		_ = c.<PERSON>fault<PERSON>("market", "cn") // 暂时未使用
		limitStr := c.DefaultQuery("limit", "10")
		minHotnessStr := c.DefaultQuery("min_hotness", "0")
		sortBy := c.DefaultQuery("sort_by", "hotness")

		limit, err := strconv.Atoi(limitStr)
		if err != nil || limit <= 0 || limit > 100 {
			limit = 10
		}

		minHotness, err := strconv.ParseFloat(minHotnessStr, 64)
		if err != nil || minHotness < 0 || minHotness > 100 {
			minHotness = 0
		}

		// 验证排序字段
		validSortFields := map[string]string{
			"hotness":      "sh.hotness",
			"today_change": "sh.today_change",
			"turnover":     "sh.turnover",
		}
		sortField, ok := validSortFields[sortBy]
		if !ok {
			sortField = "sh.hotness"
		}

		// 构建SQL查询
		query := fmt.Sprintf(`
			SELECT 
				sh.sector_id,
				s.name,
				s.name_en,
				sh.hotness,
				sh.today_change,
				sh.turnover,
				sh.volume_ratio,
				sh.pct_up,
				sh.pct_down,
				sh.top_stocks,
				sh.factors,
				sh.ts as updated_at
			FROM sector_hotness sh
			JOIN sectors s ON sh.sector_id = s.id
			WHERE sh.hotness >= ?
			ORDER BY %s DESC
			LIMIT ?
		`, sortField)

		rows, err := db.Query(query, minHotness, limit)
		if err != nil {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "database_error",
				Message: "获取热点板块失败",
			})
			return
		}
		defer rows.Close()

		var hotSectors []models.HotSector
		for rows.Next() {
			var sector models.HotSector
			var topStocksStr, factorsStr sql.NullString

			err := rows.Scan(
				&sector.SectorID,
				&sector.Name,
				&sector.NameEn,
				&sector.Hotness,
				&sector.TodayChange,
				&sector.Turnover,
				&sector.VolumeRatio,
				&sector.PctUp,
				&sector.PctDown,
				&topStocksStr,
				&factorsStr,
				&sector.UpdatedAt,
			)
			if err != nil {
				continue
			}

			// 处理JSON字段
			if topStocksStr.Valid {
				sector.TopStocks = json.RawMessage(topStocksStr.String)
			} else {
				sector.TopStocks = json.RawMessage("[]")
			}

			if factorsStr.Valid {
				sector.Factors = json.RawMessage(factorsStr.String)
			} else {
				sector.Factors = json.RawMessage("{}")
			}

			hotSectors = append(hotSectors, sector)
		}

		c.JSON(http.StatusOK, hotSectors)
	}
}

// GetSectorDetail 获取板块详情
// @Summary 获取板块详情
// @Description 获取指定板块的详细信息
// @Tags 热点板块
// @Accept json
// @Produce json
// @Param sectorId path string true "板块ID"
// @Success 200 {object} models.SectorDetail
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /hot-sectors/{sectorId} [get]
func GetSectorDetail(db *sql.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		sectorID := c.Param("sectorId")

		// 获取板块基本信息
		var detail models.SectorDetail
		err := db.QueryRow(`
			SELECT id, name, name_en, description, parent_id
			FROM sectors 
			WHERE id = ? AND is_active = 1
		`, sectorID).Scan(
			&detail.SectorID,
			&detail.Name,
			&detail.NameEn,
			&detail.Description,
			&detail.ParentID,
		)

		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "not_found",
				Message: "板块不存在",
			})
			return
		} else if err != nil {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "database_error",
				Message: "查询板块信息失败",
			})
			return
		}

		// 获取最新热度信息
		var factorsStr sql.NullString
		err = db.QueryRow(`
			SELECT hotness, today_change, turnover, volume_ratio, pct_up, pct_down, 
				   factors, ts as updated_at
			FROM sector_hotness 
			WHERE sector_id = ?
			ORDER BY ts DESC
			LIMIT 1
		`, sectorID).Scan(
			&detail.Hotness,
			&detail.TodayChange,
			&detail.Turnover,
			&detail.VolumeRatio,
			&detail.PctUp,
			&detail.PctDown,
			&factorsStr,
			&detail.UpdatedAt,
		)

		if err != nil && err != sql.ErrNoRows {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "database_error",
				Message: "查询热度信息失败",
			})
			return
		}

		// 处理因子JSON
		if factorsStr.Valid {
			detail.Factors = json.RawMessage(factorsStr.String)
		} else {
			detail.Factors = json.RawMessage("{}")
		}

		// 获取板块成员
		members, err := getSectorMembers(db, sectorID, 0, 100)
		if err != nil {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "database_error",
				Message: "查询板块成员失败",
			})
			return
		}

		detail.Members = members
		c.JSON(http.StatusOK, detail)
	}
}

// GetSectorMembers 获取板块成员
// @Summary 获取板块成员
// @Description 获取指定板块的成员股票列表
// @Tags 热点板块
// @Accept json
// @Produce json
// @Param sectorId path string true "板块ID"
// @Param sort_by query string false "排序字段" default(weight)
// @Param order query string false "排序方向" default(desc)
// @Param limit query int false "返回数量限制" default(100)
// @Param offset query int false "偏移量" default(0)
// @Success 200 {array} models.SectorMember
// @Failure 500 {object} models.ErrorResponse
// @Router /hot-sectors/{sectorId}/members [get]
func GetSectorMembers(db *sql.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		sectorID := c.Param("sectorId")

		// 解析查询参数
		limitStr := c.DefaultQuery("limit", "100")
		offsetStr := c.DefaultQuery("offset", "0")

		limit, err := strconv.Atoi(limitStr)
		if err != nil || limit <= 0 || limit > 500 {
			limit = 100
		}

		offset, err := strconv.Atoi(offsetStr)
		if err != nil || offset < 0 {
			offset = 0
		}

		members, err := getSectorMembers(db, sectorID, offset, limit)
		if err != nil {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "database_error",
				Message: "获取板块成员失败",
			})
			return
		}

		c.JSON(http.StatusOK, members)
	}
}

// getSectorMembers 内部函数：获取板块成员
func getSectorMembers(db *sql.DB, sectorID string, offset, limit int) ([]models.SectorMember, error) {
	query := `
		SELECT sm.symbol, s.name, sm.weight, sm.is_leader,
			   sk.open, sk.close, sk.high, sk.low, sk.volume, sk.amount,
			   CASE WHEN sk.open > 0 THEN (sk.close - sk.open) / sk.open * 100 ELSE 0 END as change_pct,
			   sk.ts as last_update
		FROM sector_members sm
		JOIN stocks s ON sm.symbol = s.symbol
		LEFT JOIN (
			SELECT symbol, open, close, high, low, volume, amount, ts,
				   ROW_NUMBER() OVER (PARTITION BY symbol ORDER BY ts DESC) as rn
			FROM stock_kline 
			WHERE period = '1d'
		) sk ON sm.symbol = sk.symbol AND sk.rn = 1
		WHERE sm.sector_id = ?
		ORDER BY sm.is_leader DESC, sm.weight DESC
		LIMIT ? OFFSET ?
	`

	rows, err := db.Query(query, sectorID, limit, offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var members []models.SectorMember
	for rows.Next() {
		var member models.SectorMember
		err := rows.Scan(
			&member.Symbol,
			&member.Name,
			&member.Weight,
			&member.IsLeader,
			&member.OpenPrice,
			&member.CurrentPrice,
			&member.HighPrice,
			&member.LowPrice,
			&member.Volume,
			&member.Amount,
			&member.ChangePct,
			&member.LastUpdate,
		)
		if err != nil {
			continue
		}

		members = append(members, member)
	}

	return members, nil
}

// GetSectorHistory 获取板块热度历史
// @Summary 获取板块热度历史
// @Description 获取指定板块的热度历史数据
// @Tags 热点板块
// @Accept json
// @Produce json
// @Param sectorId path string true "板块ID"
// @Param days query int false "历史天数" default(7)
// @Param interval query string false "时间间隔" default(1h)
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} models.ErrorResponse
// @Router /hot-sectors/{sectorId}/history [get]
func GetSectorHistory(db *sql.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		sectorID := c.Param("sectorId")
		daysStr := c.DefaultQuery("days", "7")
		interval := c.DefaultQuery("interval", "1h")

		days, err := strconv.Atoi(daysStr)
		if err != nil || days <= 0 || days > 365 {
			days = 7
		}

		// 时间格式映射
		timeFormats := map[string]string{
			"1h": "%Y-%m-%d %H:00:00",
			"4h": "%Y-%m-%d %H:00:00",
			"1d": "%Y-%m-%d 00:00:00",
		}
		timeFormat, ok := timeFormats[interval]
		if !ok {
			timeFormat = "%Y-%m-%d %H:00:00"
		}

		query := fmt.Sprintf(`
			SELECT 
				strftime('%s', ts) as time_bucket,
				AVG(hotness) as avg_hotness,
				AVG(today_change) as avg_change,
				SUM(turnover) as total_turnover,
				AVG(volume_ratio) as avg_volume_ratio,
				COUNT(*) as data_points
			FROM sector_hotness
			WHERE sector_id = ? 
			AND ts >= datetime('now', '-%d days')
			GROUP BY strftime('%s', ts)
			ORDER BY time_bucket
		`, timeFormat, days, timeFormat)

		rows, err := db.Query(query, sectorID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "database_error",
				Message: "获取历史数据失败",
			})
			return
		}
		defer rows.Close()

		var data []map[string]interface{}
		for rows.Next() {
			var timeBucket string
			var avgHotness, avgChange, totalTurnover, avgVolumeRatio sql.NullFloat64
			var dataPoints int

			err := rows.Scan(&timeBucket, &avgHotness, &avgChange, &totalTurnover, &avgVolumeRatio, &dataPoints)
			if err != nil {
				continue
			}

			item := map[string]interface{}{
				"time_bucket":      timeBucket,
				"avg_hotness":      nullFloat64ToFloat(avgHotness),
				"avg_change":       nullFloat64ToFloat(avgChange),
				"total_turnover":   nullFloat64ToFloat(totalTurnover),
				"avg_volume_ratio": nullFloat64ToFloat(avgVolumeRatio),
				"data_points":      dataPoints,
			}

			data = append(data, item)
		}

		response := map[string]interface{}{
			"sector_id": sectorID,
			"interval":  interval,
			"days":      days,
			"data":      data,
		}

		c.JSON(http.StatusOK, response)
	}
}

// RefreshSectorHotness 刷新板块热度
// @Summary 刷新板块热度
// @Description 手动触发指定板块的热度重新计算
// @Tags 热点板块
// @Accept json
// @Produce json
// @Param sectorId path string true "板块ID"
// @Success 200 {object} models.SuccessResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /hot-sectors/{sectorId}/refresh [post]
func RefreshSectorHotness(db *sql.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		sectorID := c.Param("sectorId")

		// 验证板块是否存在
		var exists bool
		err := db.QueryRow("SELECT EXISTS(SELECT 1 FROM sectors WHERE id = ? AND is_active = 1)", sectorID).Scan(&exists)
		if err != nil {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "database_error",
				Message: "查询板块失败",
			})
			return
		}

		if !exists {
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error:   "not_found",
				Message: "板块不存在",
			})
			return
		}

		// TODO: 实际实现中应该触发热度重新计算
		// 这里只是返回成功响应

		c.JSON(http.StatusOK, models.SuccessResponse{
			Message: fmt.Sprintf("板块 %s 热度刷新请求已提交", sectorID),
			Data: map[string]interface{}{
				"sector_id": sectorID,
				"timestamp": getCurrentTimestamp(),
			},
		})
	}
}

// 辅助函数
func nullFloat64ToFloat(nf sql.NullFloat64) float64 {
	if nf.Valid {
		return nf.Float64
	}
	return 0.0
}

func getCurrentTimestamp() string {
	return fmt.Sprintf("%d", time.Now().Unix())
}
