import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import LayoutTwoToneSvg from "@ant-design/icons-svg/es/asn/LayoutTwoTone";
import AntdIcon from "../components/AntdIcon";
var LayoutTwoTone = function LayoutTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: LayoutTwoToneSvg
  }));
};

/**![layout](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM4NCAxODVoNDU2djEzNkgzODR6bS0yMDAgMGgxMzZ2NjU2SDE4NHptNjk2LTczSDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MWMwLTE3LjcgMTQuMy0zMiAzMi0zMmg3MzZjMTcuNyAwIDMyIDE0LjMgMzIgMzJ2LTFjMC0xNy43LTE0LjMtMzItMzItMzJ6TTM4NCAzODVoNDU2djQ1NkgzODR6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik04ODAgMTEzSDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NzM2YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDczNmMxNy43IDAgMzItMTQuMyAzMi0zMlYxNDVjMC0xNy43LTE0LjMtMzItMzItMzJ6TTMyMCA4NDFIMTg0VjE4NWgxMzZ2NjU2em01MjAgMEgzODRWMzg1aDQ1NnY0NTZ6bTAtNTIwSDM4NFYxODVoNDU2djEzNnoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(LayoutTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LayoutTwoTone';
}
export default RefIcon;