import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import GoogleSquareFilledSvg from "@ant-design/icons-svg/es/asn/GoogleSquareFilled";
import AntdIcon from "../components/AntdIcon";
var GoogleSquareFilled = function GoogleSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: GoogleSquareFilledSvg
  }));
};

/**![google-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNjc5IDY5Ny42QzYzOC40IDczNSA1ODMgNzU3IDUxNi45IDc1N2MtOTUuNyAwLTE3OC41LTU0LjktMjE4LjgtMTM0LjlBMjQ1LjAyIDI0NS4wMiAwIDAxMjcyIDUxMmMwLTM5LjYgOS41LTc3IDI2LjEtMTEwLjEgNDAuMy04MC4xIDEyMy4xLTEzNSAyMTguOC0xMzUgNjYgMCAxMjEuNCAyNC4zIDE2My45IDYzLjhMNjEwLjYgNDAxYy0yNS40LTI0LjMtNTcuNy0zNi42LTkzLjYtMzYuNi02My44IDAtMTE3LjggNDMuMS0xMzcuMSAxMDEtNC45IDE0LjctNy43IDMwLjQtNy43IDQ2LjZzMi44IDMxLjkgNy43IDQ2LjZjMTkuMyA1Ny45IDczLjMgMTAxIDEzNyAxMDEgMzMgMCA2MS04LjcgODIuOS0yMy40IDI2LTE3LjQgNDMuMi00My4zIDQ4LjktNzRINTE2Ljl2LTk0LjhoMjMwLjdjMi45IDE2LjEgNC40IDMyLjggNC40IDUwLjEgMCA3NC43LTI2LjcgMTM3LjQtNzMgMTgwLjF6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(GoogleSquareFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'GoogleSquareFilled';
}
export default RefIcon;