import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FolderAddFilledSvg from "@ant-design/icons-svg/es/asn/FolderAddFilled";
import AntdIcon from "../components/AntdIcon";
var FolderAddFilled = function FolderAddFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: FolderAddFilledSvg
  }));
};

/**![folder-add](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAyOTguNEg1MjFMNDAzLjcgMTg2LjJhOC4xNSA4LjE1IDAgMDAtNS41LTIuMkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjU5MmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg3MzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzMwLjRjMC0xNy43LTE0LjMtMzItMzItMzJ6TTYzMiA1NzdjMCAzLjgtMy40IDctNy41IDdINTQwdjg0LjljMCAzLjktMy4yIDcuMS03IDcuMWgtNDJjLTMuOCAwLTctMy4yLTctNy4xVjU4NGgtODQuNWMtNC4xIDAtNy41LTMuMi03LjUtN3YtNDJjMC0zLjggMy40LTcgNy41LTdINDg0di04NC45YzAtMy45IDMuMi03LjEgNy03LjFoNDJjMy44IDAgNyAzLjIgNyA3LjFWNTI4aDg0LjVjNC4xIDAgNy41IDMuMiA3LjUgN3Y0MnoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(FolderAddFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FolderAddFilled';
}
export default RefIcon;