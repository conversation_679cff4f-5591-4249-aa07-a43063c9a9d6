import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FacebookFilledSvg from "@ant-design/icons-svg/es/asn/FacebookFilled";
import AntdIcon from "../components/AntdIcon";
var FacebookFilled = function FacebookFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: FacebookFilledSvg
  }));
};

/**![facebook](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTkyLjQgMjMzLjVoLTYzLjljLTUwLjEgMC01OS44IDIzLjgtNTkuOCA1OC44djc3LjFoMTE5LjZsLTE1LjYgMTIwLjdoLTEwNFY5MTJINTM5LjJWNjAyLjJINDM0LjlWNDgxLjRoMTA0LjN2LTg5YzAtMTAzLjMgNjMuMS0xNTkuNiAxNTUuMy0xNTkuNiA0NC4yIDAgODIuMSAzLjMgOTMuMiA0Ljh2MTA3Ljl6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(FacebookFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FacebookFilled';
}
export default RefIcon;