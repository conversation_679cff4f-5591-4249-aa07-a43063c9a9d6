import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FieldStringOutlinedSvg from "@ant-design/icons-svg/es/asn/FieldStringOutlined";
import AntdIcon from "../components/AntdIcon";
var FieldStringOutlined = function FieldStringOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: FieldStringOutlinedSvg
  }));
};

/**![field-string](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik04NzUuNiA1MTUuOWMyLjEuOCA0LjQtLjMgNS4yLTIuNC4yLS40LjItLjkuMi0xLjR2LTU4LjNjMC0xLjgtMS4xLTMuMy0yLjgtMy44LTYtMS44LTE3LjItMy0yNy4yLTMtMzIuOSAwLTYxLjcgMTYuNy03My41IDQxLjJ2LTI4LjZjMC00LjQtMy42LTgtOC04SDcxN2MtNC40IDAtOCAzLjYtOCA4VjcyOWMwIDQuNCAzLjYgOCA4IDhoNTQuOGM0LjQgMCA4LTMuNiA4LThWNTcyLjdjMC0zNi4yIDI2LjEtNjAuMiA2NS4xLTYwLjIgMTAuNC4xIDI2LjYgMS44IDMwLjcgMy40em0tNTM3LTQwLjVsLTU0LjctMTIuNmMtNjEuMi0xNC4yLTg3LjctMzQuOC04Ny43LTcwLjcgMC00NC42IDM5LjEtNzMuNSA5Ni45LTczLjUgNTIuOCAwIDkxLjQgMjYuNSA5OS45IDY4LjloNzBDNDU1LjkgMzExLjYgMzg3LjYgMjU5IDI5My40IDI1OWMtMTAzLjMgMC0xNzEgNTUuNS0xNzEgMTM5IDAgNjguNiAzOC42IDEwOS41IDEyMi4yIDEyOC41bDYxLjYgMTQuM2M2My42IDE0LjkgOTEuNiAzNy4xIDkxLjYgNzUuMSAwIDQ0LjEtNDMuNSA3NS4yLTEwMi41IDc1LjItNjAuNiAwLTEwNC41LTI3LjItMTEyLjgtNzAuNUgxMTFjNy4yIDc5LjkgNzUuNiAxMzAuNCAxNzkuMSAxMzAuNEM0MDIuMyA3NTEgNDcxIDY5NS4yIDQ3MSA2MDUuM2MwLTcwLjItMzguNi0xMDguNS0xMzIuNC0xMjkuOXpNODQxIDcyOWEzNiAzNiAwIDEwNzIgMCAzNiAzNiAwIDEwLTcyIDB6TTY1MyA0NTcuOGgtNTEuNFYzOTZjMC00LjQtMy42LTgtOC04aC01NC43Yy00LjQgMC04IDMuNi04IDh2NjEuOEg0OTVjLTQuNCAwLTggMy42LTggOHY0Mi4zYzAgNC40IDMuNiA4IDggOGgzNS45djE0Ny41YzAgNTYuMiAyNy40IDc5LjQgOTMuMSA3OS40IDExLjcgMCAyMy42LTEuMiAzMy44LTMuMSAxLjktLjMgMy4yLTIgMy4yLTMuOXYtNDkuM2MwLTIuMi0xLjgtNC00LTRoLS40Yy00LjkuNS02LjIuNi04LjMuOC00LjEuMy03LjguNS0xMi42LjUtMjQuMSAwLTM0LjEtMTAuMy0zNC4xLTM1LjZWNTE2LjFINjUzYzQuNCAwIDgtMy42IDgtOHYtNDIuM2MwLTQuNC0zLjYtOC04LTh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(FieldStringOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FieldStringOutlined';
}
export default RefIcon;