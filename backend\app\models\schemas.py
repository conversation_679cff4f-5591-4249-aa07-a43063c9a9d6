"""
Pydantic模型定义
用于API请求和响应的数据模型
"""

from pydantic import BaseModel, Field, EmailStr
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class MarketType(str, Enum):
    """市场类型"""
    CN = "cn"  # A股
    US = "us"  # 美股


class AlertType(str, Enum):
    """告警类型"""
    SECTOR_HOTNESS = "sector_hotness"
    STOCK_PRICE = "stock_price" 
    VOLUME_SPIKE = "volume_spike"


class ConditionType(str, Enum):
    """条件类型"""
    GT = ">"
    LT = "<"
    GTE = ">="
    LTE = "<="


# 用户相关模型
class UserBase(BaseModel):
    """用户基础模型"""
    email: EmailStr
    username: Optional[str] = None
    is_active: bool = True


class UserCreate(UserBase):
    """用户创建模型"""
    password: str = Field(..., min_length=6, description="密码至少6位")


class UserUpdate(BaseModel):
    """用户更新模型"""
    username: Optional[str] = None
    avatar_url: Optional[str] = None


class UserResponse(UserBase):
    """用户响应模型"""
    id: int
    avatar_url: Optional[str] = None
    is_premium: bool = False
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class UserLogin(BaseModel):
    """用户登录模型"""
    email: EmailStr
    password: str


class Token(BaseModel):
    """令牌模型"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int


# 股票相关模型
class StockBase(BaseModel):
    """股票基础模型"""
    symbol: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")
    sector_id: Optional[str] = Field(None, description="所属板块ID")
    market: MarketType = Field(..., description="市场类型")


class StockResponse(StockBase):
    """股票响应模型"""
    list_date: Optional[str] = None
    market_cap: Optional[float] = None
    is_active: bool = True
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class StockKline(BaseModel):
    """K线数据模型"""
    symbol: str
    ts: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    amount: float
    period: str = Field(..., description="周期: 1m, 5m, 15m, 1h, 1d")


class StockKlineResponse(StockKline):
    """K线响应模型"""
    id: int
    created_at: datetime

    class Config:
        from_attributes = True


# 板块相关模型
class SectorBase(BaseModel):
    """板块基础模型"""
    id: str = Field(..., description="板块ID")
    name: str = Field(..., description="板块名称")
    name_en: Optional[str] = Field(None, description="英文名称")
    description: Optional[str] = Field(None, description="板块描述")


class SectorResponse(SectorBase):
    """板块响应模型"""
    parent_id: Optional[str] = None
    industry_code: Optional[str] = None
    is_active: bool = True
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class SectorMemberResponse(BaseModel):
    """板块成员响应模型"""
    symbol: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")
    weight: float = Field(..., description="权重")
    is_leader: bool = Field(..., description="是否为龙头")
    current_price: Optional[float] = Field(None, description="当前价格")
    open_price: Optional[float] = Field(None, description="开盘价")
    high_price: Optional[float] = Field(None, description="最高价")
    low_price: Optional[float] = Field(None, description="最低价")
    volume: Optional[int] = Field(None, description="成交量")
    amount: Optional[float] = Field(None, description="成交额")
    change_pct: Optional[float] = Field(None, description="涨跌幅%")
    last_update: Optional[datetime] = Field(None, description="最后更新时间")


# 热点板块模型
class HotSectorResponse(BaseModel):
    """热点板块响应模型"""
    sector_id: str = Field(..., description="板块ID")
    name: str = Field(..., description="板块名称")
    name_en: Optional[str] = Field(None, description="英文名称")
    hotness: float = Field(..., description="热度分数 0-100")
    today_change: Optional[float] = Field(None, description="今日涨跌幅%")
    turnover: Optional[int] = Field(None, description="成交额")
    volume_ratio: Optional[float] = Field(None, description="量比")
    pct_up: Optional[float] = Field(None, description="上涨股票比例")
    pct_down: Optional[float] = Field(None, description="下跌股票比例")
    top_stocks: List[str] = Field(default_factory=list, description="龙头股票列表")
    factors: Dict[str, float] = Field(default_factory=dict, description="热度因子分解")
    updated_at: datetime = Field(..., description="更新时间")


class SectorDetailResponse(SectorBase):
    """板块详情响应模型"""
    parent_id: Optional[str] = None
    industry_code: Optional[str] = None
    hotness: float = Field(..., description="热度分数")
    today_change: Optional[float] = Field(None, description="今日涨跌幅%")
    turnover: Optional[int] = Field(None, description="成交额")
    volume_ratio: Optional[float] = Field(None, description="量比")
    pct_up: Optional[float] = Field(None, description="上涨股票比例")
    pct_down: Optional[float] = Field(None, description="下跌股票比例")
    factors: Dict[str, float] = Field(default_factory=dict, description="热度因子")
    members: List[SectorMemberResponse] = Field(default_factory=list, description="板块成员")
    updated_at: Optional[datetime] = Field(None, description="更新时间")


# 资金流向模型
class FundFlowResponse(BaseModel):
    """资金流向响应模型"""
    symbol: str = Field(..., description="股票代码")
    ts: datetime = Field(..., description="时间")
    net_inflow: Optional[float] = Field(None, description="净流入")
    main_inflow: Optional[float] = Field(None, description="主力净流入")
    retail_inflow: Optional[float] = Field(None, description="散户净流入")
    sector_id: Optional[str] = Field(None, description="所属板块")


# 新闻模型
class NewsResponse(BaseModel):
    """新闻响应模型"""
    id: str = Field(..., description="新闻ID")
    title: str = Field(..., description="标题")
    content: Optional[str] = Field(None, description="内容")
    published_at: datetime = Field(..., description="发布时间")
    source: Optional[str] = Field(None, description="来源")
    url: Optional[str] = Field(None, description="链接")
    symbols: List[str] = Field(default_factory=list, description="相关股票")
    sentiment_score: Optional[float] = Field(None, description="情感分数")
    keywords: List[str] = Field(default_factory=list, description="关键词")


# 用户偏好模型
class UserPreferencesResponse(BaseModel):
    """用户偏好响应模型"""
    user_id: int
    default_market: MarketType = MarketType.CN
    watched_sectors: List[str] = Field(default_factory=list)
    watched_stocks: List[str] = Field(default_factory=list)
    hotness_threshold: float = 70.0
    alert_email: bool = True
    alert_push: bool = True
    theme: str = "light"
    language: str = "zh-CN"
    created_at: datetime
    updated_at: datetime


class UserPreferencesUpdate(BaseModel):
    """用户偏好更新模型"""
    default_market: Optional[MarketType] = None
    watched_sectors: Optional[List[str]] = None
    watched_stocks: Optional[List[str]] = None
    hotness_threshold: Optional[float] = Field(None, ge=0, le=100)
    alert_email: Optional[bool] = None
    alert_push: Optional[bool] = None
    theme: Optional[str] = None
    language: Optional[str] = None


# 告警配置模型
class AlertConfigCreate(BaseModel):
    """告警配置创建模型"""
    alert_type: AlertType
    target_type: str = Field(..., description="目标类型: sector, stock")
    target_id: str = Field(..., description="目标ID")
    condition_type: ConditionType
    threshold_value: float
    is_active: bool = True


class AlertConfigResponse(AlertConfigCreate):
    """告警配置响应模型"""
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AlertHistoryResponse(BaseModel):
    """告警历史响应模型"""
    id: int
    user_id: int
    alert_config_id: int
    triggered_at: datetime
    message: str
    current_value: Optional[float] = None
    threshold_value: Optional[float] = None
    is_read: bool = False
    created_at: datetime

    class Config:
        from_attributes = True


# LLM相关模型
class LLMRequest(BaseModel):
    """LLM请求模型"""
    question: str = Field(..., description="问题")
    context_type: str = Field(..., description="上下文类型: sector, stock, market")
    context_id: Optional[str] = Field(None, description="上下文ID")
    model_name: Optional[str] = Field(None, description="模型名称")
    max_tokens: Optional[int] = Field(None, description="最大token数")
    temperature: Optional[float] = Field(None, description="温度参数")


class LLMResponse(BaseModel):
    """LLM响应模型"""
    answer: str = Field(..., description="回答")
    references: List[Dict[str, Any]] = Field(default_factory=list, description="参考信息")
    confidence: Optional[float] = Field(None, description="置信度")
    token_usage: Optional[int] = Field(None, description="使用的token数")
    processing_time_ms: Optional[int] = Field(None, description="处理时间毫秒")
    model_name: str = Field(..., description="使用的模型")


# WebSocket消息模型
class WSMessage(BaseModel):
    """WebSocket消息模型"""
    type: str = Field(..., description="消息类型")
    data: Dict[str, Any] = Field(default_factory=dict, description="消息数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")


class WSSubscribeRequest(BaseModel):
    """WebSocket订阅请求模型"""
    type: str = "subscribe"
    channels: List[str] = Field(..., description="订阅频道列表")


# 系统统计模型
class SystemStatsResponse(BaseModel):
    """系统统计响应模型"""
    total_stocks: int = Field(..., description="股票总数")
    total_sectors: int = Field(..., description="板块总数")
    total_users: int = Field(..., description="用户总数")
    active_connections: int = Field(..., description="活跃连接数")
    data_collector_status: bool = Field(..., description="数据采集器状态")
    hotness_calculator_status: bool = Field(..., description="热度计算器状态")
    database_size_mb: float = Field(..., description="数据库大小MB")
    last_update: Optional[datetime] = Field(None, description="最后更新时间")


# 数据库健康检查模型
class DatabaseHealthResponse(BaseModel):
    """数据库健康检查响应模型"""
    status: str = Field(..., description="状态")
    connection: str = Field(..., description="连接状态")
    database_size_bytes: int = Field(..., description="数据库大小字节")
    database_size_mb: float = Field(..., description="数据库大小MB")
    table_count: int = Field(..., description="表数量")
    tables: List[str] = Field(..., description="表列表")


# 通用响应模型
class MessageResponse(BaseModel):
    """通用消息响应模型"""
    message: str = Field(..., description="消息内容")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    data: Optional[Dict[str, Any]] = Field(None, description="附加数据")


class PaginatedResponse(BaseModel):
    """分页响应模型"""
    items: List[Any] = Field(..., description="数据项")
    total: int = Field(..., description="总数")
    page: int = Field(..., description="当前页")
    page_size: int = Field(..., description="页大小")
    total_pages: int = Field(..., description="总页数")

    @classmethod
    def create(cls, items: List[Any], total: int, page: int, page_size: int):
        """创建分页响应"""
        total_pages = (total + page_size - 1) // page_size
        return cls(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )

