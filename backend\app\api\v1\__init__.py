"""
API v1 路由模块
"""

from fastapi import APIRouter
from app.api.v1 import hot_sectors, stocks, users, system, llm

# 创建API路由器
api_router = APIRouter()

# 包含各个模块的路由
api_router.include_router(hot_sectors.router, prefix="/hot-sectors", tags=["热点板块"])
api_router.include_router(stocks.router, prefix="/stocks", tags=["股票数据"])
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])
api_router.include_router(system.router, prefix="/system", tags=["系统管理"])
api_router.include_router(llm.router, prefix="/llm", tags=["AI问答"])


@api_router.get("/")
async def api_root():
    """API根路径"""
    return {
        "message": "AI炒股网站 API v1",
        "version": "1.0.0",
        "docs": "/docs",
        "endpoints": {
            "hot_sectors": "/api/v1/hot-sectors",
            "stocks": "/api/v1/stocks", 
            "users": "/api/v1/users",
            "system": "/api/v1/system",
            "llm": "/api/v1/llm"
        }
    }
