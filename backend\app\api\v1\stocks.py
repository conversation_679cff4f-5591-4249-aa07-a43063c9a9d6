"""
股票数据API
"""

from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional
from datetime import datetime, timedelta

from app.core.database import db_manager
from app.models.schemas import StockResponse, StockKlineResponse
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.get("/", response_model=List[StockResponse])
async def get_stocks(
    market: str = Query("cn", description="市场代码"),
    sector_id: Optional[str] = Query(None, description="板块ID"),
    limit: int = Query(100, ge=1, le=500),
    offset: int = Query(0, ge=0)
):
    """获取股票列表"""
    try:
        where_conditions = ["is_active = 1"]
        params = {"limit": limit, "offset": offset}
        
        if market != "all":
            where_conditions.append("market = :market")
            params["market"] = market
            
        if sector_id:
            where_conditions.append("sector_id = :sector_id") 
            params["sector_id"] = sector_id
            
        sql = f"""
        SELECT symbol, name, sector_id, market, list_date, market_cap, 
               is_active, created_at, updated_at
        FROM stocks
        WHERE {' AND '.join(where_conditions)}
        ORDER BY symbol
        LIMIT :limit OFFSET :offset
        """
        
        results = await db_manager.execute_raw_sql(sql, params)
        return results
        
    except Exception as e:
        logger.error(f"获取股票列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取股票列表失败")


@router.get("/{symbol}/kline", response_model=List[StockKlineResponse])
async def get_stock_kline(
    symbol: str,
    period: str = Query("1d", description="周期"),
    days: int = Query(30, ge=1, le=365, description="天数")
):
    """获取股票K线数据"""
    try:
        start_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        sql = """
        SELECT id, symbol, ts, open, high, low, close, volume, amount, period, created_at
        FROM stock_kline
        WHERE symbol = :symbol AND period = :period AND ts >= :start_date
        ORDER BY ts
        """
        
        params = {
            "symbol": symbol,
            "period": period, 
            "start_date": start_date
        }
        
        results = await db_manager.execute_raw_sql(sql, params)
        return results
        
    except Exception as e:
        logger.error(f"获取K线数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取K线数据失败")


@router.get("/{symbol}")
async def get_stock_detail(symbol: str):
    """获取股票详情"""
    try:
        # 基本信息
        stock_sql = """
        SELECT * FROM stocks WHERE symbol = :symbol AND is_active = 1
        """
        stock = await db_manager.execute_raw_sql_single(stock_sql, {"symbol": symbol})
        
        if not stock:
            raise HTTPException(status_code=404, detail="股票不存在")
            
        # 最新价格
        price_sql = """
        SELECT * FROM stock_kline 
        WHERE symbol = :symbol AND period = '1d'
        ORDER BY ts DESC LIMIT 1
        """
        price_data = await db_manager.execute_raw_sql_single(price_sql, {"symbol": symbol})
        
        return {
            "stock_info": stock,
            "latest_price": price_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取股票详情失败")

