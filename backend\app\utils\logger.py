"""
日志配置模块
"""

import logging
import sys
from typing import Optional
from loguru import logger
from app.core.config import settings


def setup_logger(name: Optional[str] = None) -> logging.Logger:
    """设置日志配置"""
    
    # 移除默认的loguru处理器
    logger.remove()
    
    # 添加控制台输出
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level=settings.LOG_LEVEL,
        colorize=True
    )
    
    # 如果配置了日志文件，添加文件输出
    if settings.LOG_FILE:
        logger.add(
            settings.LOG_FILE,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level=settings.LOG_LEVEL,
            rotation="10 MB",
            retention="7 days",
            compression="zip"
        )
    
    # 创建标准的logging.Logger以便与其他库兼容
    class InterceptHandler(logging.Handler):
        def emit(self, record):
            # 获取对应的loguru级别
            try:
                level = logger.level(record.levelname).name
            except ValueError:
                level = record.levelno

            # 查找调用者
            frame, depth = logging.currentframe(), 2
            while frame.f_code.co_filename == logging.__file__:
                frame = frame.f_back
                depth += 1

            logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())
    
    # 设置标准logging使用loguru
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
    
    if name:
        return logging.getLogger(name)
    else:
        return logging.getLogger(__name__)


def get_logger(name: str) -> logging.Logger:
    """获取logger实例"""
    return logging.getLogger(name)

