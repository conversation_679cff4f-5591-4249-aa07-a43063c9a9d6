import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FunnelPlotOutlinedSvg from "@ant-design/icons-svg/es/asn/FunnelPlotOutlined";
import AntdIcon from "../components/AntdIcon";
var FunnelPlotOutlined = function FunnelPlotOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: FunnelPlotOutlinedSvg
  }));
};

/**![funnel-plot](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MC4xIDE1NEgxNDMuOWMtMjQuNSAwLTM5LjggMjYuNy0yNy41IDQ4TDM0OSA2MDcuNFY4MzhjMCAxNy43IDE0LjIgMzIgMzEuOCAzMmgyNjIuNGMxNy42IDAgMzEuOC0xNC4zIDMxLjgtMzJWNjA3LjRMOTA3LjcgMjAyYzEyLjItMjEuMy0zLjEtNDgtMjcuNi00OHpNNjAzLjQgNzk4SDQyMC42VjY1MGgxODIuOXYxNDh6bTkuNi0yMjYuNmwtOC40IDE0LjZINDE5LjNsLTguNC0xNC42TDMzNC40IDQzOGgzNTUuMkw2MTMgNTcxLjR6TTcyNi4zIDM3NEgyOTcuN2wtODUtMTQ4aDU5OC42bC04NSAxNDh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(FunnelPlotOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FunnelPlotOutlined';
}
export default RefIcon;